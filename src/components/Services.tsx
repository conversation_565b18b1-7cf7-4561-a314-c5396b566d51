import React from 'react';
import { Music, MapPin, Utensils, Sparkles, Mic, Palette, Users, Camera } from 'lucide-react';

const Services = () => {
  const services = [
    {
      icon: <Palette size={40} />,
      title: 'Event Decor & Design',
      description: 'Transform your vision into stunning reality with our bespoke decoration and design services.',
      features: ['Custom themes', 'Floral arrangements', 'Lighting design', 'Furniture rentals']
    },
    {
      icon: <Camera size={40} />,
      title: 'Photography & Videography',
      description: 'Capture every precious moment with our professional photography and videography services.',
      features: ['Professional photographers', 'Cinematic videography', 'Drone photography', 'Same-day highlights']
    },
    {
      icon: <MapPin size={40} />,
      title: 'Venue Selection',
      description: 'Find the perfect setting for your special day from our curated selection of premium venues.',
      features: ['Location scouting', 'Venue negotiations', 'Site visits', 'Permits & licensing']
    },
    {
      icon: <Utensils size={40} />,
      title: 'Catering & Cuisine',
      description: 'Delight your guests with exceptional culinary experiences tailored to your taste and dietary needs.',
      features: ['Custom menus', 'Dietary accommodations', 'Professional service', 'Bar services']
    },
    {
      icon: <Music size={40} />,
      title: 'Entertainment & DJ',
      description: 'Keep the energy high with our professional DJs and curated entertainment options.',
      features: ['Professional DJs', 'Sound systems', 'Live performers', 'Custom playlists']
    },
    {
      icon: <Mic size={40} />,
      title: 'Master of Ceremonies',
      description: 'Ensure smooth flow with our experienced and charismatic event hosts and emcees.',
      features: ['Professional hosting', 'Timeline management', 'Guest coordination', 'Special announcements']
    },
    {
      icon: <Sparkles size={40} />,
      title: 'Special Effects',
      description: 'Enhance your event with spectacular special effects and unique entertainment.',
      features: ['Fireworks', 'LED displays', 'Fog machines', 'Custom lighting']
    },
    {
      icon: <Users size={40} />,
      title: 'Guest Coordination',
      description: 'Ensure your guests have an exceptional experience from arrival to departure.',
      features: ['Guest services', 'Transportation', 'Accommodation', 'Welcome packages']
    }
  ];

  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Complete Event Solutions
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We handle every aspect of your event, from initial concept to final cleanup, 
            ensuring a seamless and unforgettable experience.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div 
              key={index}
              className="bg-pearl-50 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group border border-champagne-100"
            >
              <div className="mb-4 group-hover:scale-110 transition-all duration-300" style={{ color: '#d8a80c' }}>
                {service.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {service.title}
              </h3>
              <p className="text-gray-600 mb-4 leading-relaxed">
                {service.description}
              </p>
              <ul className="space-y-2">
                {service.features.map((feature, idx) => (
                  <li key={idx} className="flex items-center text-sm text-gray-500">
                    <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: '#d8a80c' }}></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <a 
            href="#contact"
            className="inline-flex items-center px-8 py-4 text-white rounded-full font-semibold hover:shadow-2xl transition-all duration-300 transform hover:scale-110"
            style={{ backgroundColor: '#ecb70b' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#d8a80c'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#ecb70b'}
          >
            Get Custom Quote
          </a>
        </div>
      </div>
    </section>
  );
};

export default Services;