import React from 'react';
import { Facebook, Instagram, Twitter, Linkedin, Mail, Phone, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <h3 className="text-3xl font-black mb-4 tracking-wide drop-shadow-lg" style={{ color: '#ecb70b' }}>
              Aurum Vesaire
            </h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              We specialize in creating extraordinary events that celebrate life's most precious moments. 
              From intimate gatherings to grand celebrations, we bring your vision to life with 
              unparalleled attention to detail and creative excellence.
            </p>
            
            <div className="flex space-x-4">
              <a 
                href="#" 
                className="w-10 h-10 rounded-full flex items-center justify-center hover:shadow-xl transition-all duration-300 transform hover:scale-110"
                style={{ backgroundColor: '#ecb70b' }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#d8a80c'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#ecb70b'}
              >
                <Facebook size={18} />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 rounded-full flex items-center justify-center hover:shadow-xl transition-all duration-300 transform hover:scale-110"
                style={{ backgroundColor: '#ecb70b' }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#d8a80c'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#ecb70b'}
              >
                <Instagram size={18} />
              </a> 
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li><a href="#home" className="text-gray-300 hover:text-yellow-400 transition-colors duration-200">Home</a></li>
              <li><a href="#services" className="text-gray-300 hover:text-yellow-400 transition-colors duration-200">Services</a></li>
              <li><a href="#portfolio" className="text-gray-300 hover:text-yellow-400 transition-colors duration-200">Portfolio</a></li>
              <li><a href="#about" className="text-gray-300 hover:text-yellow-400 transition-colors duration-200">About</a></li>
              <li><a href="#calendar" className="text-gray-300 hover:text-yellow-400 transition-colors duration-200">Book Us</a></li>
              <li><a href="#contact" className="text-gray-300 hover:text-yellow-400 transition-colors duration-200">Contact</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
            <div className="space-y-3">
              <div className="flex items-center text-gray-300">
                <Mail size={16} className="mr-3 text-yellow-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center text-gray-300">
                <Phone size={16} className="mr-3 text-yellow-400" />
                <span>(*************</span>
              </div>
            </div>

            <div className="mt-6">
              <h5 className="text-sm font-medium mb-2">Business Hours</h5>
              <div className="text-sm text-gray-400">
                <p>Monday - Friday: 9AM - 8PM</p>
                <p>Saturday - Sunday: 10AM - 6PM</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Aurum Vesaire. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-yellow-400 text-sm transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-yellow-400 text-sm transition-colors duration-200">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-yellow-400 text-sm transition-colors duration-200">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;