import React, { useState } from 'react';
import { Plus, X, Upload, Camera } from 'lucide-react';

const Portfolio = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [showUpload, setShowUpload] = useState(false);

  // Sample portfolio images - these would be replaced with actual uploads
  const portfolioImages = [
    {
      id: 1,
      src: 'https://images.pexels.com/photos/1024993/pexels-photo-1024993.jpeg?auto=compress&cs=tinysrgb&w=800',
      title: 'Elegant Wedding Reception',
      category: 'Wedding'
    },
    {
      id: 2,
      src: 'https://images.pexels.com/photos/1190298/pexels-photo-1190298.jpeg?auto=compress&cs=tinysrgb&w=800',
      title: 'Corporate Gala Dinner',
      category: 'Corporate'
    },
    {
      id: 3,
      src: 'https://images.pexels.com/photos/1024992/pexels-photo-1024992.jpeg?auto=compress&cs=tinysrgb&w=800',
      title: 'Birthday Celebration',
      category: 'Private Party'
    },
    {
      id: 4,
      src: 'https://images.pexels.com/photos/1190297/pexels-photo-1190297.jpeg?auto=compress&cs=tinysrgb&w=800',
      title: 'Product Launch Event',
      category: 'Corporate'
    },
    {
      id: 5,
      src: 'https://images.pexels.com/photos/1024960/pexels-photo-1024960.jpeg?auto=compress&cs=tinysrgb&w=800',
      title: 'Luxury Garden Party',
      category: 'Private Party'
    },

  ];

  const categories = ['All', 'Wedding', 'Corporate', 'Private Party'];
  const [activeCategory, setActiveCategory] = useState('All');

  const filteredImages = activeCategory === 'All' 
    ? portfolioImages 
    : portfolioImages.filter(img => img.category === activeCategory);

  const UploadModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold">Upload Event Photos</h3>
          <button 
            onClick={() => setShowUpload(false)}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>
        
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-yellow-400 transition-colors duration-200">
          <Camera size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600 mb-2">Drag and drop your images here</p>
          <p className="text-sm text-gray-500 mb-4">or click to browse</p>
          <input 
            type="file" 
            multiple 
            accept="image/*" 
            className="hidden" 
            id="file-upload"
          />
          <label 
            htmlFor="file-upload"
            className="inline-flex items-center px-4 py-2 text-white rounded-lg cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:scale-110"
            style={{ backgroundColor: '#ecb70b' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#d8a80c'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#ecb70b'}
          >
            <Upload size={16} className="mr-2" />
            Choose Files
          </label>
        </div>
        
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Event Title
          </label>
          <input 
            type="text" 
            placeholder="Enter event title"
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
          />
        </div>
        
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Category
          </label>
          <select className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500">
            <option>Wedding</option>
            <option>Corporate</option>
            <option>Private Party</option>
          </select>
        </div>
        
        <div className="flex space-x-3 mt-6">
          <button 
            onClick={() => setShowUpload(false)}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </button>
          <button 
            className="flex-1 px-4 py-2 text-white rounded-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110" 
            style={{ backgroundColor: '#ecb70b' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#d8a80c'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#ecb70b'}
          >
            Upload
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <section id="portfolio" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Our Portfolio
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Explore our portfolio of extraordinary events. Each celebration tells a unique story 
            of elegance, creativity, and unforgettable moments.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-6 py-2 rounded-full font-medium transition-all duration-200 ${
                activeCategory === category
                  ? 'bg-bronze-400 text-white shadow-lg'
                  : 'bg-oldlace-100 text-darkbrown-700 hover:bg-oldlace-200 hover:text-bronze-400'
              }`}
            >
              {category}
            </button>
          ))}
        </div>



        {/* Image Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredImages.map((image) => (
            <div
              key={image.id}
              className="group relative overflow-hidden rounded-xl shadow-lg cursor-pointer transform transition-all duration-300 hover:scale-105"
              onClick={() => setSelectedImage(image.src)}
            >
              <img
                src={image.src}
                alt={image.title}
                className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-4 left-4 text-white">
                  <h3 className="text-lg font-semibold">{image.title}</h3>
                  <p className="text-sm text-gray-200">{image.category}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Lightbox */}
        {selectedImage && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div className="relative max-w-4xl max-h-full">
              <img
                src={selectedImage}
                alt="Portfolio"
                className="max-w-full max-h-full object-contain"
              />
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors duration-200"
              >
                <X size={32} />
              </button>
            </div>
          </div>
        )}

        {/* Upload Modal */}
        {showUpload && <UploadModal />}
      </div>
    </section>
  );
};

export default Portfolio;