import React, { useState } from 'react';
import { Mail, Phone, Clock, Send } from 'lucide-react';
import emailjs from '@emailjs/browser';
import { EMAILJS_CONFIG } from '../config/emailjs';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  eventType: string;
  eventDate: string;
  eventMonth: string;
  eventDay: string;
  eventYear: string;
  budget: string;
  message: string;
}

interface FormErrors {
  contact?: string;
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  eventType?: string;
  eventDate?: string;
  message?: string;
  [key: string]: string | undefined;
}

const Contact = () => {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    eventType: '',
    eventDate: '',
    eventMonth: '',
    eventDay: '',
    eventYear: '',
    budget: '',
    message: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  };

  const validateName = (name: string): boolean => {
    return name.trim().length >= 2 && /^[a-zA-Z\s'-]+$/.test(name.trim());
  };

  const validateEventDate = (date: string): boolean => {
    if (!date) return true; // Optional field
    const selectedDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return selectedDate >= today;
  };

  const validateMessage = (message: string): boolean => {
    return message.trim().length >= 10;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newData = {
        ...prev,
        [name]: value
      };
      
      // Update eventDate when month, day, or year changes
      if (name === 'eventMonth' || name === 'eventDay' || name === 'eventYear') {
        const month = name === 'eventMonth' ? value : prev.eventMonth;
        const day = name === 'eventDay' ? value : prev.eventDay;
        const year = name === 'eventYear' ? value : prev.eventYear;
        
        if (month && day && year) {
          newData.eventDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        } else {
          newData.eventDate = '';
        }
      }
      
      return newData;
    });

    // Clear error when user starts typing
    if (errors[name] || errors.eventDate) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
        eventDate: ''
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const newErrors: FormErrors = {};

    // Validate required fields
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required.';
    } else if (!validateName(formData.firstName)) {
      newErrors.firstName = 'Please enter a valid first name (at least 2 characters, letters only).';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required.';
    } else if (!validateName(formData.lastName)) {
      newErrors.lastName = 'Please enter a valid last name (at least 2 characters, letters only).';
    }

    // Validate that at least one contact method is provided and valid
    const hasValidEmail = formData.email && validateEmail(formData.email);
    const hasValidPhone = formData.phone && validatePhone(formData.phone);

    if (!hasValidEmail && !hasValidPhone) {
      if (!formData.email && !formData.phone) {
        newErrors.contact = 'Please provide either a valid email address or phone number - both are mandatory for us to contact you back.';
      } else if (formData.email && !hasValidEmail) {
        newErrors.email = 'Please provide a valid email address (e.g., <EMAIL>).';
      } else if (formData.phone && !hasValidPhone) {
        newErrors.phone = 'Please provide a valid phone number (e.g., ******-123-4567).';
      }
    } else {
      // Individual field validation for better UX
      if (formData.email && !hasValidEmail) {
        newErrors.email = 'Please provide a valid email address (e.g., <EMAIL>).';
      }
      if (formData.phone && !hasValidPhone) {
        newErrors.phone = 'Please provide a valid phone number (e.g., ******-123-4567).';
      }
    }

    // Validate event type (required for better service)
    if (!formData.eventType) {
      newErrors.eventType = 'Please select an event type to help us serve you better.';
    }

    // Validate event date
    if (formData.eventDate && !validateEventDate(formData.eventDate)) {
      newErrors.eventDate = 'Please select a future date for your event.';
    }

    // Validate message (required for better understanding)
    if (!formData.message.trim()) {
      newErrors.message = 'Please tell us about your vision to help us create the perfect event.';
    } else if (!validateMessage(formData.message)) {
      newErrors.message = 'Please provide more details (at least 10 characters) about your event vision.';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      // Check if EmailJS is properly configured
      const isEmailJSConfigured =
        EMAILJS_CONFIG.SERVICE_ID !== 'YOUR_SERVICE_ID' &&
        EMAILJS_CONFIG.TEMPLATE_ID !== 'YOUR_TEMPLATE_ID' &&
        EMAILJS_CONFIG.PUBLIC_KEY !== 'YOUR_PUBLIC_KEY';

      if (!isEmailJSConfigured) {
        // EmailJS not configured yet - show form data and instructions
        console.log('Form submitted (EmailJS not configured):', formData);
        alert(`Thank you for your message! 

Form Details:
Name: ${formData.firstName} ${formData.lastName}
Email: ${formData.email}
Phone: ${formData.phone}
Event: ${formData.eventType || 'Not specified'}
Date: ${formData.eventDate || 'Not specified'}
Budget: ${formData.budget || 'Not specified'}
Message: ${formData.message || 'No message'}

Note: EmailJS is not configured yet. Please check the console and EMAILJS_SETUP.md for setup instructions.`);

        // Reset form fields
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          eventType: '',
          eventDate: '',
          eventMonth: '',
          eventDay: '',
          eventYear: '',
          budget: '',
          message: ''
        });
        setErrors({});
        return;
      }

      // EmailJS is configured, proceed with email sending
      setIsSubmitting(true);

      // Prepare email template parameters
      const templateParams = {
        from_name: `${formData.firstName} ${formData.lastName}`.trim(),
        from_email: formData.email,
        phone: formData.phone,
        event_type: formData.eventType || 'Not specified',
        event_date: formData.eventDate || 'Not specified',
        budget: formData.budget || 'Not specified',
        message: formData.message || 'No additional message provided',
        to_email: '<EMAIL>'
      };

      emailjs.send(EMAILJS_CONFIG.SERVICE_ID, EMAILJS_CONFIG.TEMPLATE_ID, templateParams, EMAILJS_CONFIG.PUBLIC_KEY)
        .then(() => {
          alert('Thank you for your message! We will get back to you soon.');

          // Reset form fields after successful submission
          setFormData({
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            eventType: '',
            eventDate: '',
            eventMonth: '',
            eventDay: '',
            eventYear: '',
            budget: '',
            message: ''
          });

          // Clear any remaining errors
          setErrors({});
        })
        .catch((error) => {
          console.error('EmailJS Error:', error);
          alert('Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>');
        })
        .finally(() => {
          setIsSubmitting(false);
        });
    }
  };
  const contactInfo = [
    {
      icon: <Mail size={24} />,
      title: 'Email Us',
      details: '<EMAIL>',
      subtitle: 'We respond within 24 hours'
    },
    {
      icon: <Phone size={24} />,
      title: 'Call Us',
      details: '(*************',
      subtitle: 'Available during business hours'
    },
    {
      icon: <Clock size={24} />,
      title: 'Business Hours',
      details: 'Mon-Fri: 9AM-8PM',
      subtitle: 'Sat-Sun: 10AM-6PM'
    }
  ];

  return (
    <section id="contact" className="py-20 bg-oldlace-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Let's Create Magic Together
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Ready to start planning your extraordinary event? Get in touch with our team
            and let's bring your vision to life.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Get In Touch</h3>

            <div className="space-y-6">
              {contactInfo.map((info, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-champagne-100 rounded-lg flex items-center justify-center" style={{ color: '#FFD700' }}>
                    {info.icon}
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">
                      {info.title}
                    </h4>
                    <p className="text-gray-700 font-medium">
                      {info.details}
                    </p>
                    <p className="text-gray-500 text-sm">
                      {info.subtitle}
                    </p>
                  </div>
                </div>
              ))}
            </div>


          </div>

          {/* Contact Form */}
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Send Us a Message</h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              {errors.contact && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-600 text-sm">{errors.contact}</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name *
                  </label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    placeholder="Enter your first name"
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 ${
                      errors.firstName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    required
                  />
                  {errors.firstName && (
                    <p className="text-red-600 text-sm mt-1">{errors.firstName}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    placeholder="Enter your last name"
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 ${
                      errors.lastName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    required
                  />
                  {errors.lastName && (
                    <p className="text-red-600 text-sm mt-1">{errors.lastName}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Enter your email address"
                  className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 ${errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                />
                {errors.email && (
                  <p className="text-red-600 text-sm mt-1">{errors.email}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="Enter your phone number"
                  className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 ${errors.phone ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                />
                {errors.phone && (
                  <p className="text-red-600 text-sm mt-1">{errors.phone}</p>
                )}
                <p className="text-gray-500 text-xs mt-1">
                  * Please provide either a valid email address or phone number
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Event Type *
                </label>
                <select
                  name="eventType"
                  value={formData.eventType}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 appearance-none bg-white ${
                    errors.eventType ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  required
                >
                  <option value="" disabled>Choose the type of event you're planning</option>
                  <option value="wedding">💒 Wedding Ceremony & Reception</option>
                  <option value="corporate">🏢 Corporate Event & Business Function</option>
                  <option value="birthday">🎂 Birthday Party & Celebration</option>
                  <option value="anniversary">💕 Anniversary & Milestone Celebration</option>
                  <option value="engagement">💍 Engagement Party & Proposal</option>
                  <option value="baby-shower">👶 Baby Shower & Gender Reveal</option>
                  <option value="graduation">🎓 Graduation & Achievement Party</option>
                  <option value="holiday">🎄 Holiday & Seasonal Celebration</option>
                  <option value="fundraiser">🤝 Fundraiser & Charity Event</option>
                  <option value="other">✨ Other Special Celebration</option>
                </select>
                {errors.eventType && (
                  <p className="text-red-600 text-sm mt-1">{errors.eventType}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Preferred Event Date
                </label>
                <div className="grid grid-cols-3 gap-4">
                  {/* Month Dropdown */}
                  <div>
                    <label className="block text-xs font-medium text-gray-500 mb-1">Month</label>
                    <select
                      name="eventMonth"
                      value={formData.eventMonth}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-4 text-lg border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 appearance-none bg-white font-medium ${
                        errors.eventDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      style={{ fontSize: '16px', height: '60px' }}
                    >
                      <option value="" disabled>Month</option>
                      <option value="1">🌟 January</option>
                      <option value="2">💕 February</option>
                      <option value="3">🌸 March</option>
                      <option value="4">🌷 April</option>
                      <option value="5">🌺 May</option>
                      <option value="6">☀️ June</option>
                      <option value="7">🌻 July</option>
                      <option value="8">🌞 August</option>
                      <option value="9">🍂 September</option>
                      <option value="10">🎃 October</option>
                      <option value="11">🦃 November</option>
                      <option value="12">❄️ December</option>
                    </select>
                  </div>

                  {/* Day Dropdown */}
                  <div>
                    <label className="block text-xs font-medium text-gray-500 mb-1">Day</label>
                    <select
                      name="eventDay"
                      value={formData.eventDay}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-4 text-lg border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 appearance-none bg-white font-medium ${
                        errors.eventDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      style={{ fontSize: '16px', height: '60px' }}
                      disabled={!formData.eventMonth}
                    >
                      <option value="" disabled>Day</option>
                      {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                        <option key={day} value={day.toString()}>{day}</option>
                      ))}
                    </select>
                  </div>

                  {/* Year Dropdown */}
                  <div>
                    <label className="block text-xs font-medium text-gray-500 mb-1">Year</label>
                    <select
                      name="eventYear"
                      value={formData.eventYear}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-4 text-lg border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 appearance-none bg-white font-medium ${
                        errors.eventDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      style={{ fontSize: '16px', height: '60px' }}
                    >
                      <option value="" disabled>Year</option>
                      {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() + i).map(year => (
                        <option key={year} value={year.toString()}>{year}</option>
                      ))}
                    </select>
                  </div>
                </div>
                {errors.eventDate && (
                  <p className="text-red-600 text-sm mt-2">{errors.eventDate}</p>
                )}
                <p className="text-gray-500 text-xs mt-2">
                  📅 Select your preferred date (we can discuss alternatives during consultation)
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Investment Range
                </label>
                <select
                  name="budget"
                  value={formData.budget}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 appearance-none bg-white"
                >
                  <option value="" disabled>Select your investment range for this event</option>
                  <option value="5k-15k">💰 $5,000 - $15,000 (Intimate Gathering)</option>
                  <option value="15k-30k">💎 $15,000 - $30,000 (Elegant Celebration)</option>
                  <option value="30k-60k">✨ $30,000 - $60,000 (Luxury Experience)</option>
                  <option value="60k-100k">👑 $60,000 - $100,000 (Premium Event)</option>
                  <option value="100k+">🌟 $100,000+ (Ultra-Luxury Affair)</option>
                  <option value="flexible">🤝 Flexible - Let's Discuss Options</option>
                </select>
                <p className="text-gray-500 text-xs mt-1">
                  This helps us tailor our recommendations to your vision and needs
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tell Us About Your Vision *
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={5}
                  placeholder="Share your dream event details: preferred style, number of guests, venue preferences, special requirements, color themes, must-have elements, or any inspiration you have in mind..."
                  className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-all duration-200 resize-none ${
                    errors.message ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  required
                ></textarea>
                {errors.message && (
                  <p className="text-red-600 text-sm mt-1">{errors.message}</p>
                )}
                <p className="text-gray-500 text-xs mt-1">
                  The more details you share, the better we can tailor our proposal to your vision
                </p>
              </div>

              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full px-8 py-5 rounded-xl font-bold text-lg transition-all duration-300 shadow-xl flex items-center justify-center whitespace-nowrap ${
                    isSubmitting
                      ? 'bg-gray-400 cursor-not-allowed text-gray-600'
                      : 'text-white hover:shadow-2xl transform hover:scale-110'
                  }`}
                  style={!isSubmitting ? { backgroundColor: '#ecb70b' } : {}}
                  onMouseEnter={!isSubmitting ? (e) => e.currentTarget.style.backgroundColor = '#d8a80c' : undefined}
                  onMouseLeave={!isSubmitting ? (e) => e.currentTarget.style.backgroundColor = '#ecb70b' : undefined}
                >
                  <Send size={24} className="mr-3" />
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Sending Request...
                    </>
                  ) : (
                    'Book Dream Event ✨'
                  )}
                </button>
                <p className="text-center text-gray-500 text-sm mt-3">
                  We'll respond within 24 hours with a personalized proposal
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;