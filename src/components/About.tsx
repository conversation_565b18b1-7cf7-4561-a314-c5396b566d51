import React from 'react';
import { Users, Award, Heart, Star } from 'lucide-react';

const About = () => {

  return (
    <section id="about" className="py-20 bg-oldlace-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-darkbrown-700 mb-4">
            About Aurum Vesaire Events
          </h2>
          <p className="text-xl text-darkbrown-600 max-w-3xl mx-auto">
            We are passionate event planners dedicated to creating extraordinary experiences 
            that reflect your unique style and vision. Every celebration tells a story, 
            and we're here to help you tell yours beautifully.
          </p>
        </div>

        {/* Our Story */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          <div>
            <h3 className="text-3xl font-bold text-darkbrown-700 mb-6">Our Story</h3>
            <p className="text-darkbrown-600 mb-6">
              Founded with a passion for creating unforgettable moments, Aurum Vesaire Events 
              has been transforming celebrations into extraordinary experiences for over a decade. 
              Our name, reflects our commitment to bringing golden moments to life.
            </p>
            <p className="text-darkbrown-600 mb-6">
              We believe that every event should be as unique as the people celebrating it. 
              From intimate gatherings to grand celebrations, we work closely with our clients 
              to understand their vision and bring it to life with meticulous attention to detail.
            </p>
            <p className="text-darkbrown-600">
              Our team of experienced professionals is dedicated to making your special day 
              stress-free and absolutely perfect. We handle every aspect of planning and 
              coordination, so you can focus on what matters most - celebrating with your loved ones.
            </p>
          </div>
          <div className="relative">
            <img 
              src="https://images.pexels.com/photos/3171837/pexels-photo-3171837.jpeg?auto=compress&cs=tinysrgb&w=800"
              alt="Event planning team at work"
              className="rounded-2xl shadow-xl"
            />
            <div className="absolute inset-0 bg-bronze-400/10 rounded-2xl"></div>
          </div>
        </div>


      </div>
    </section>
  );
};

export default About;