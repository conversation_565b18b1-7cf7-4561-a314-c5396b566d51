import React, { useState, useEffect } from 'react';
import { ArrowR<PERSON>, Star, ChevronLeft, ChevronRight } from 'lucide-react';

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      image: 'https://images.pexels.com/photos/3014019/pexels-photo-3014019.jpeg?auto=compress&cs=tinysrgb&w=1600',
      title: 'Mehendi Celebrations',
      subtitle: 'Intricate henna art and joyful festivities'
    },
    {
      image: 'https://images.pexels.com/photos/3585798/pexels-photo-3585798.jpeg?auto=compress&cs=tinysrgb&w=1600',
      title: 'Sangam Ceremonies',
      subtitle: 'Traditional rituals and family celebrations'
    },
    {
      image: 'https://images.pexels.com/photos/2959192/pexels-photo-2959192.jpeg?auto=compress&cs=tinysrgb&w=1600',
      title: 'Hindu Ceremonies',
      subtitle: 'Sacred fire rituals and blessed unions'
    },
    {
      image: 'https://images.pexels.com/photos/1190298/pexels-photo-1190298.jpeg?auto=compress&cs=tinysrgb&w=1600',
      title: 'Private Parties',
      subtitle: 'Intimate celebrations with personal touch'
    },
    {
      image: 'https://images.pexels.com/photos/1024960/pexels-photo-1024960.jpeg?auto=compress&cs=tinysrgb&w=1600',
      title: 'Gender Reveals',
      subtitle: 'Magical moments of discovery'
    },
    {
      image: 'https://images.pexels.com/photos/587741/pexels-photo-587741.jpeg?auto=compress&cs=tinysrgb&w=1600',
      title: 'Baby Showers',
      subtitle: 'Celebrating new beginnings'
    },
    {
      image: 'https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=1600',
      title: 'Coffee Raves',
      subtitle: 'Energetic gatherings with great vibes'
    }
  ];

  // Auto-advance slides
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Slideshow Background */}
      <div className="absolute inset-0 z-0">
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
            style={{
              backgroundImage: `linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.2)), url("${slide.image}")`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
        ))}
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 backdrop-blur-sm"
      >
        <ChevronLeft size={24} />
      </button>
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 backdrop-blur-sm"
      >
        <ChevronRight size={24} />
      </button>

      {/* Content */}
      <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <div className="inline-block px-16 py-8 rounded-xl backdrop-blur-sm mb-8" style={{ backgroundColor: 'rgba(0, 0, 0, 0.15)' }}>
          <h1 className="text-5xl md:text-7xl leading-tight" style={{ fontFamily: 'Playfair Display, Times New Roman, serif', fontWeight: '600', letterSpacing: '3px', textTransform: 'uppercase' }}>
            <span className="block drop-shadow-2xl" style={{ color: '#ecb70b' }}>
              Aurum Vesaire
            </span>
            <span className="block drop-shadow-2xl" style={{ color: '#ecb70b' }}>
              Events
            </span>
          </h1>
        </div>
        
        <p className="text-xl md:text-2xl mb-8 text-white max-w-xl md:max-w-2xl lg:max-w-3xl mx-auto leading-relaxed" style={{ fontFamily: 'Playfair Display, Georgia, serif', fontWeight: '300', letterSpacing: '0.5px' }}>
          From intimate gatherings to grand celebrations, we orchestrate every detail 
          to perfection, creating memories that last a lifetime.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-8 justify-center">
          <a 
            href="#services"
            className="inline-flex items-center justify-center px-8 py-4 text-white rounded-full font-semibold hover:shadow-2xl transition-all duration-300 transform hover:scale-110"
            style={{ backgroundColor: '#ecb70b' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#d8a80c'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#ecb70b'}
          >
            Explore Our Services
            <ArrowRight className="ml-2" size={20} />
          </a>
          <a 
            href="#calendar"
            className="inline-flex items-center justify-center px-8 py-4 text-white rounded-full font-semibold hover:shadow-2xl transition-all duration-300 transform hover:scale-110"
            style={{ backgroundColor: '#ecb70b' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#d8a80c'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#ecb70b'}
          >
            Book Consultation
          </a>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide 
                ? 'bg-bronze-400 scale-125' 
                : 'bg-white/50 hover:bg-white/75'
            }`}
          />
        ))}
      </div>

      {/* Animated scroll indicator */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <div className="flex flex-col items-center">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;