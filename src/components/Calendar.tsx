import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Clock, User, Phone, Mail, Send } from 'lucide-react';
import emailjs from '@emailjs/browser';
import { EMAILJS_CONFIG } from '../config/emailjs';

interface BookingFormData {
  fullName: string;
  phone: string;
  email: string;
  preferredTime: string;
  eventType: string;
  notes: string;
}

interface BookingFormErrors {
  fullName?: string;
  phone?: string;
  email?: string;
  preferredTime?: string;
  eventType?: string;
  notes?: string;
  [key: string]: string | undefined;
}

// Booking Modal Component - moved outside to prevent re-creation
interface BookingModalProps {
  selectedDate: Date | null;
  formData: BookingFormData;
  errors: BookingFormErrors;
  isSubmitting: boolean;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  onClose: () => void;
}

const BookingModal: React.FC<BookingModalProps> = ({
  selectedDate,
  formData,
  errors,
  isSubmitting,
  onInputChange,
  onSubmit,
  onClose
}) => (
  <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
    <div className="bg-white rounded-xl max-w-lg w-full p-8 max-h-[90vh] overflow-y-auto">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-2xl font-bold text-gray-900">Book Your Consultation</h3>
        <button 
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 text-2xl font-bold w-8 h-8 flex items-center justify-center"
          disabled={isSubmitting}
        >
          ×
        </button>
      </div>

      <div className="mb-6 p-4 rounded-lg" style={{ 
        background: 'linear-gradient(to right, #FFFBF0, #FFF8E1)', 
        border: '1px solid #FFE082' 
      }}>
        <p className="font-medium" style={{ color: '#B8860B' }}>
          📅 Selected Date: {selectedDate?.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </p>
        <p className="text-sm mt-1" style={{ color: '#DAA520' }}>
          Free consultation to discuss your event vision and get a personalized quote
        </p>
      </div>

      <form onSubmit={onSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Full Name *
          </label>
          <div className="relative">
            <User size={16} className="absolute left-3 top-3 text-gray-400" />
            <input 
              type="text" 
              name="fullName"
              value={formData.fullName}
              onChange={onInputChange}
              placeholder="Enter your full name"
              className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${
                errors.fullName ? 'border-red-300 bg-red-50' : 'border-gray-300'
              }`}
              style={{ 
                '--tw-ring-color': '#FFD700' 
              } as React.CSSProperties}
              onFocus={(e) => {
                e.target.style.borderColor = '#FFD700';
                e.target.style.boxShadow = '0 0 0 2px rgba(255, 215, 0, 0.2)';
              }}
              onBlur={(e) => {
                if (!errors.fullName) {
                  e.target.style.borderColor = '#D1D5DB';
                  e.target.style.boxShadow = 'none';
                }
              }}
              required
            />
          </div>
          {errors.fullName && (
            <p className="text-red-600 text-sm mt-1">{errors.fullName}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Phone Number *
          </label>
          <div className="relative">
            <Phone size={16} className="absolute left-3 top-3 text-gray-400" />
            <input 
              type="tel" 
              name="phone"
              value={formData.phone}
              onChange={onInputChange}
              placeholder="Enter your phone number"
              className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${
                errors.phone ? 'border-red-300 bg-red-50' : 'border-gray-300'
              }`}
              onFocus={(e) => {
                e.target.style.borderColor = '#FFD700';
                e.target.style.boxShadow = '0 0 0 2px rgba(255, 215, 0, 0.2)';
              }}
              onBlur={(e) => {
                if (!errors.phone) {
                  e.target.style.borderColor = '#D1D5DB';
                  e.target.style.boxShadow = 'none';
                }
              }}
              required
            />
          </div>
          {errors.phone && (
            <p className="text-red-600 text-sm mt-1">{errors.phone}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address *
          </label>
          <div className="relative">
            <Mail size={16} className="absolute left-3 top-3 text-gray-400" />
            <input 
              type="email" 
              name="email"
              value={formData.email}
              onChange={onInputChange}
              placeholder="Enter your email address"
              className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${
                errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
              }`}
              onFocus={(e) => {
                e.target.style.borderColor = '#FFD700';
                e.target.style.boxShadow = '0 0 0 2px rgba(255, 215, 0, 0.2)';
              }}
              onBlur={(e) => {
                if (!errors.email) {
                  e.target.style.borderColor = '#D1D5DB';
                  e.target.style.boxShadow = 'none';
                }
              }}
              required
            />
          </div>
          {errors.email && (
            <p className="text-red-600 text-sm mt-1">{errors.email}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preferred Time *
          </label>
          <div className="relative">
            <Clock size={16} className="absolute left-3 top-3 text-gray-400" />
            <select 
              name="preferredTime"
              value={formData.preferredTime}
              onChange={onInputChange}
              className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 appearance-none bg-white ${
                errors.preferredTime ? 'border-red-300 bg-red-50' : 'border-gray-300'
              }`}
              required
            >
              <option value="" disabled>Select your preferred consultation time</option>
              <option value="9:00 AM">🌅 9:00 AM - Early Morning</option>
              <option value="10:00 AM">☀️ 10:00 AM - Morning</option>
              <option value="12:00 PM">🌞 12:00 PM - Noon</option>
              <option value="2:00 PM">🌤️ 2:00 PM - Afternoon</option>
              <option value="4:00 PM">🌇 4:00 PM - Late Afternoon</option>
              <option value="6:00 PM">🌆 6:00 PM - Evening</option>
            </select>
          </div>
          {errors.preferredTime && (
            <p className="text-red-600 text-sm mt-1">{errors.preferredTime}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Event Type *
          </label>
          <select 
            name="eventType"
            value={formData.eventType}
            onChange={onInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 appearance-none bg-white ${
              errors.eventType ? 'border-red-300 bg-red-50' : 'border-gray-300'
            }`}
            required
          >
            <option value="" disabled>Choose the type of event you're planning</option>
            <option value="wedding">💒 Wedding Ceremony & Reception</option>
            <option value="corporate">🏢 Corporate Event & Business Function</option>
            <option value="birthday">🎂 Birthday Party & Celebration</option>
            <option value="anniversary">💕 Anniversary & Milestone Celebration</option>
            <option value="engagement">💍 Engagement Party & Proposal</option>
            <option value="baby-shower">👶 Baby Shower & Gender Reveal</option>
            <option value="graduation">🎓 Graduation & Achievement Party</option>
            <option value="holiday">🎄 Holiday & Seasonal Celebration</option>
            <option value="fundraiser">🤝 Fundraiser & Charity Event</option>
            <option value="other">✨ Other Special Celebration</option>
          </select>
          {errors.eventType && (
            <p className="text-red-600 text-sm mt-1">{errors.eventType}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tell Us About Your Vision *
          </label>
          <textarea 
            name="notes"
            value={formData.notes}
            onChange={onInputChange}
            placeholder="Share your event vision: preferred style, number of guests, venue preferences, budget range, special requirements, or any inspiration you have in mind..."
            rows={4}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 resize-none ${
              errors.notes ? 'border-red-300 bg-red-50' : 'border-gray-300'
            }`}
            required
          ></textarea>
          {errors.notes && (
            <p className="text-red-600 text-sm mt-1">{errors.notes}</p>
          )}
          <p className="text-gray-500 text-xs mt-1">
            This helps us prepare for a more productive consultation
          </p>
        </div>

        <div className="flex space-x-3 mt-6">
          <button 
            type="button"
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button 
            type="submit"
            disabled={isSubmitting}
            className={`flex-1 px-4 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center whitespace-nowrap ${
              isSubmitting
                ? 'bg-gray-400 cursor-not-allowed text-gray-600'
                : 'text-white hover:shadow-2xl transform hover:scale-110'
            }`}
            style={!isSubmitting ? { backgroundColor: '#ecb70b' } : {}}
            onMouseEnter={!isSubmitting ? (e) => e.currentTarget.style.backgroundColor = '#d8a80c' : undefined}
            onMouseLeave={!isSubmitting ? (e) => e.currentTarget.style.backgroundColor = '#ecb70b' : undefined}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Booking...
              </>
            ) : (
              <>
                <Send size={16} className="mr-2" />
                Book Now ✨
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  </div>
);

const Calendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showBooking, setShowBooking] = useState(false);
  const [formData, setFormData] = useState<BookingFormData>({
    fullName: '',
    phone: '',
    email: '',
    preferredTime: '',
    eventType: '',
    notes: ''
  });
  const [errors, setErrors] = useState<BookingFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDay = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDay; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const isDateAvailable = (date: Date | null) => {
    if (!date) return false;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date >= today;
  };

  const isDateSelected = (date: Date | null) => {
    if (!date || !selectedDate) return false;
    return date.toDateString() === selectedDate.toDateString();
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  };

  const validateName = (name: string): boolean => {
    return name.trim().length >= 2 && /^[a-zA-Z\s'-]+$/.test(name.trim());
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const newErrors: BookingFormErrors = {};

    // Validate required fields
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required.';
    } else if (!validateName(formData.fullName)) {
      newErrors.fullName = 'Please enter a valid full name (at least 2 characters, letters only).';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email address is required.';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please provide a valid email address (e.g., <EMAIL>).';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required.';
    } else if (!validatePhone(formData.phone)) {
      newErrors.phone = 'Please provide a valid phone number (e.g., ******-123-4567).';
    }

    if (!formData.preferredTime) {
      newErrors.preferredTime = 'Please select a preferred time for your consultation.';
    }

    if (!formData.eventType) {
      newErrors.eventType = 'Please select an event type to help us prepare for your consultation.';
    }

    if (!formData.notes.trim()) {
      newErrors.notes = 'Please tell us about your event vision to make the consultation more productive.';
    } else if (formData.notes.trim().length < 10) {
      newErrors.notes = 'Please provide more details (at least 10 characters) about your event vision.';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      // Check if EmailJS is properly configured
      const isEmailJSConfigured =
        EMAILJS_CONFIG.SERVICE_ID !== 'YOUR_SERVICE_ID' &&
        EMAILJS_CONFIG.TEMPLATE_ID !== 'YOUR_TEMPLATE_ID' &&
        EMAILJS_CONFIG.PUBLIC_KEY !== 'YOUR_PUBLIC_KEY';

      if (!isEmailJSConfigured) {
        // EmailJS not configured yet - show form data and instructions
        console.log('Consultation booking submitted (EmailJS not configured):', formData);
        alert(`Thank you for booking your consultation! 

Booking Details:
Name: ${formData.fullName}
Email: ${formData.email}
Phone: ${formData.phone}
Date: ${selectedDate?.toLocaleDateString()}
Time: ${formData.preferredTime}
Event Type: ${formData.eventType}
Notes: ${formData.notes}

Note: EmailJS is not configured yet. Please check the console and EMAILJS_SETUP.md for setup instructions.`);

        // Reset form and close modal
        resetForm();
        setShowBooking(false);
        return;
      }

      // EmailJS is configured, proceed with email sending
      setIsSubmitting(true);

      // Prepare email template parameters
      const templateParams = {
        from_name: formData.fullName,
        from_email: formData.email,
        phone: formData.phone,
        consultation_date: selectedDate?.toLocaleDateString(),
        preferred_time: formData.preferredTime,
        event_type: formData.eventType,
        message: formData.notes,
        to_email: '<EMAIL>',
        booking_type: 'Consultation Booking'
      };

      emailjs.send(EMAILJS_CONFIG.SERVICE_ID, EMAILJS_CONFIG.TEMPLATE_ID, templateParams, EMAILJS_CONFIG.PUBLIC_KEY)
        .then(() => {
          alert('Thank you for booking your consultation! We will contact you within 24 hours to confirm your appointment.');

          // Reset form and close modal
          resetForm();
          setShowBooking(false);
        })
        .catch((error) => {
          console.error('EmailJS Error:', error);
          alert('Sorry, there was an error booking your consultation. Please try again or contact us <NAME_EMAIL>');
        })
        .finally(() => {
          setIsSubmitting(false);
        });
    }
  };

  const days = getDaysInMonth(currentDate);

  const resetForm = () => {
    setFormData({
      fullName: '',
      phone: '',
      email: '',
      preferredTime: '',
      eventType: '',
      notes: ''
    });
    setErrors({});
  };

  const closeModal = () => {
    setShowBooking(false);
    resetForm();
  };

  return (
    <section id="calendar" className="py-24 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            Book Your Consultation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Schedule a free consultation to discuss your event vision and get a personalized quote.
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 max-w-3xl mx-auto">
          {/* Calendar Header */}
          <div className="text-white p-6" style={{ background: 'linear-gradient(135deg, #ecb70b 0%, #d8a80c 100%)' }}>
            <div className="flex items-center justify-between">
              <button
                onClick={() => navigateMonth('prev')}
                className="p-3 hover:bg-white/20 rounded-full transition-all duration-300"
              >
                <ChevronLeft size={24} />
              </button>
              <h3 className="text-2xl font-bold">
                {months[currentDate.getMonth()]} {currentDate.getFullYear()}
              </h3>
              <button
                onClick={() => navigateMonth('next')}
                className="p-3 hover:bg-white/20 rounded-full transition-all duration-300"
              >
                <ChevronRight size={24} />
              </button>
            </div>
          </div>

          {/* Calendar Grid */}
          <div className="p-6">
            {/* Day Headers */}
            <div className="grid grid-cols-7 gap-2 mb-4">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <div key={day} className="text-center font-semibold text-gray-600 py-3 text-base">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Days */}
            <div className="grid grid-cols-7 gap-2">
              {days.map((date, index) => (
                <button
                  key={index}
                  onClick={() => {
                    if (isDateAvailable(date)) {
                      setSelectedDate(date);
                      setShowBooking(true);
                    }
                  }}
                  disabled={!isDateAvailable(date)}
                  className={`
                    aspect-square p-3 text-base font-medium rounded-lg transition-all duration-200 hover:scale-105
                    ${!date ? 'invisible' : ''}
                    ${!isDateAvailable(date) 
                      ? 'text-gray-300 cursor-not-allowed bg-gray-50' 
                      : 'text-gray-700 hover:bg-yellow-50 hover:text-yellow-800 cursor-pointer hover:shadow-md'
                    }
                    ${isDateSelected(date) 
                      ? 'text-white shadow-lg transform scale-105' 
                      : ''
                    }`}
                  style={isDateSelected(date) ? { 
                    background: 'linear-gradient(135deg, #ecb70b 0%, #d8a80c 100%)'
                  } : {}}
                >
                  {date?.getDate()}
                </button>
              ))}
            </div>
          </div>

          {/* Calendar Footer */}
          <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-center text-base text-gray-600">
              <span className="font-medium">✨ Select a date to book your consultation</span>
            </div>
          </div>
        </div>

        {/* Booking Modal */}
        {showBooking && (
          <BookingModal
            selectedDate={selectedDate}
            formData={formData}
            errors={errors}
            isSubmitting={isSubmitting}
            onInputChange={handleInputChange}
            onSubmit={handleSubmit}
            onClose={closeModal}
          />
        )}
      </div>
    </section>
  );
};

export default Calendar;