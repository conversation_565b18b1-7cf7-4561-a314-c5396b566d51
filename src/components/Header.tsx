import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: 'Home', href: '#home' },
    { name: 'Services', href: '#services' },
    { name: 'Portfolio', href: '#portfolio' },
    { name: 'About', href: '#about' },
    { name: 'Contact', href: '#contact' },
    { name: 'Book Us', href: '#calendar' }
  ];

  return (
    <header className={`fixed top-0 w-full z-50 transition-all duration-300 ${isScrolled ? 'bg-white/95 backdrop-blur-sm shadow-lg' : 'bg-transparent'
      }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          <div className="flex items-center">
            <h1
              className="text-2xl font-bold text-bronze-400 font-serif tracking-wide"
              style={{ display: 'none' }}
            >
              Aurum Vesaire
            </h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className={`transition-colors duration-200 font-medium ${item.name === 'Book Us'
                  ? 'text-white px-4 py-2 rounded-full hover:shadow-lg transform hover:scale-105'
                  : isScrolled ? 'text-darkbrown-700' : 'text-white'
                  }`}
                style={item.name === 'Book Us' ? { backgroundColor: '#ecb70b' } :
                  isScrolled ? { color: '#8B4513' } :
                    { color: 'white' }}
                onMouseEnter={(e) => {
                  if (item.name === 'Book Us') {
                    e.currentTarget.style.backgroundColor = '#d8a80c';
                  } else if (isScrolled) {
                    e.currentTarget.style.color = '#ecb70b';
                  } else {
                    e.currentTarget.style.color = '#ecb70b';
                  }
                }}
                onMouseLeave={(e) => {
                  if (item.name === 'Book Us') {
                    e.currentTarget.style.backgroundColor = '#ecb70b';
                  } else if (isScrolled) {
                    e.currentTarget.style.color = '#8B4513';
                  } else {
                    e.currentTarget.style.color = 'white';
                  }
                }}
              >
                {item.name}
              </a>
            ))}
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className={`transition-colors duration-200 ${isScrolled ? 'text-darkbrown-700 hover:text-bronze-400' : 'text-white hover:text-bronze-300'
                }`}
            >
              {isOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className={`block px-3 py-2 rounded-md transition-colors duration-200 ${item.name === 'Book Us'
                    ? 'text-white hover:shadow-lg'
                    : 'text-darkbrown-700 hover:bg-oldlace-100'
                    }`}
                  onClick={() => setIsOpen(false)}
                >
                  {item.name}
                </a>
              ))}
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;