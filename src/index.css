@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Date Picker Styles */
input[type="date"] {
  position: relative;
  background: white;
  font-family: inherit;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  background: transparent;
  bottom: 0;
  color: transparent;
  cursor: pointer;
  height: auto;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: auto;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFD700' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 24px;
}

input[type="date"]::-webkit-datetime-edit {
  padding: 0;
  font-size: 16px;
  color: #374151;
}

input[type="date"]::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

input[type="date"]::-webkit-datetime-edit-text {
  color: #6B7280;
  padding: 0 4px;
}

input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-day-field,
input[type="date"]::-webkit-datetime-edit-year-field {
  padding: 0 2px;
  color: #374151;
  font-weight: 500;
}

input[type="date"]:focus::-webkit-datetime-edit-month-field,
input[type="date"]:focus::-webkit-datetime-edit-day-field,
input[type="date"]:focus::-webkit-datetime-edit-year-field {
  background-color: #FEF3C7;
  color: #92400E;
  border-radius: 4px;
}

/* Firefox date picker styling */
input[type="date"]::-moz-focus-inner {
  border: 0;
  padding: 0;
}

/* Enhanced calendar popup for webkit browsers */
input[type="date"]::-webkit-calendar-picker-indicator:hover {
  background-color: rgba(255, 215, 0, 0.1);
  border-radius: 4px;
}

/* Placeholder styling when no date is selected */
input[type="date"]:invalid::-webkit-datetime-edit {
  color: #9CA3AF;
}

input[type="date"]:invalid::before {
  content: "📅 Choose your special day";
  color: #9CA3AF;
  font-size: 16px;
}

input[type="date"]:focus:invalid::before,
input[type="date"]:valid::before {
  display: none;
}
