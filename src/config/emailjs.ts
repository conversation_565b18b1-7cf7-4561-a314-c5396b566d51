// EmailJS Configuration
// To set up EmailJS:
// 1. Go to https://www.emailjs.com/
// 2. Create an account and verify your email
// 3. Create a new service (Gmail recommended)
// 4. Create an email template
// 5. Get your Public Key from Account settings
// 6. Replace the values below with your actual credentials

export interface EmailJSConfig {
  SERVICE_ID: string;
  TEMPLATE_ID: string;
  PUBLIC_KEY: string;
}

export const EMAILJS_CONFIG: EmailJSConfig = {
  SERVICE_ID: 'service_s3o73s7', // EmailJS service ID
  TEMPLATE_ID: 'template_xutydd8', // EmailJS template ID  
  PUBLIC_KEY: 'LJ0ZcxiBPMbk6VbgD' // EmailJS public key
};

// Example email template variables you can use:
// {{from_name}} - Sender's full name
// {{from_email}} - Sender's email
// {{phone}} - Sender's phone number
// {{event_type}} - Type of event
// {{event_date}} - Event date
// {{budget}} - Budget range
// {{message}} - Custom message
// {{to_email}} - Recipient email (<EMAIL>)