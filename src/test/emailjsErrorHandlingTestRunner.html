<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmailJS Error Handling Tests</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        .test-info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            margin-top: 0;
            color: #0c5460;
        }
        .requirements {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .requirements h3 {
            margin-top: 0;
            color: #856404;
        }
        .requirements ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
            transition: background-color 0.3s;
        }
        button:hover {
            background: #005a9e;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        #testOutput {
            background: #000;
            color: #00ff00;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 600px;
            overflow-y: auto;
            margin-top: 20px;
            border: 2px solid #333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.running {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        .test-scenarios {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-scenarios h3 {
            margin-top: 0;
            color: #495057;
        }
        .scenario {
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-left: 4px solid #007acc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 EmailJS Error Handling Tests</h1>
        
        <div class="test-info">
            <h3>📋 Test Overview</h3>
            <p>This test suite validates error handling for EmailJS service issues in the Aurum Vesaire contact form. It ensures that users receive appropriate feedback when errors occur and that form data is preserved during error states.</p>
        </div>

        <div class="requirements">
            <h3>📝 Requirements Being Tested</h3>
            <ul>
                <li><strong>3.3:</strong> User-friendly error messages and console logging</li>
                <li><strong>4.1:</strong> Error handling for service unavailability</li>
                <li><strong>4.2:</strong> Network issue handling with retry suggestions</li>
                <li><strong>4.5:</strong> Form data preservation during errors</li>
            </ul>
        </div>

        <div class="test-scenarios">
            <h3>🎯 Test Scenarios</h3>
            <div class="scenario">
                <strong>1. Invalid Credentials:</strong> Tests behavior with invalid EmailJS service credentials
            </div>
            <div class="scenario">
                <strong>2. Network Errors:</strong> Simulates network connectivity issues during form submission
            </div>
            <div class="scenario">
                <strong>3. User-Friendly Messages:</strong> Verifies appropriate error messages are shown to users
            </div>
            <div class="scenario">
                <strong>4. Form Data Preservation:</strong> Ensures form data is not lost during error states
            </div>
            <div class="scenario">
                <strong>5. Console Logging:</strong> Validates that errors are properly logged for debugging
            </div>
            <div class="scenario">
                <strong>6. Loading State Management:</strong> Tests loading state cleanup during errors
            </div>
        </div>

        <div class="button-container">
            <button onclick="runTests()" id="runButton">🚀 Run All Tests</button>
            <button onclick="clearOutput()" id="clearButton">🗑️ Clear Output</button>
        </div>

        <div id="status"></div>
        <div id="testOutput"></div>
    </div>

    <script src="emailjsErrorHandlingTest.js"></script>
    <script>
        let isRunning = false;

        function updateStatus(message, type = 'running') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function appendOutput(text) {
            const output = document.getElementById('testOutput');
            output.textContent += text + '\n';
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('testOutput').textContent = '';
            document.getElementById('status').innerHTML = '';
        }

        // Override console methods to capture output
        function setupConsoleCapture() {
            const originalConsole = {
                log: console.log,
                error: console.error,
                warn: console.warn
            };

            console.log = function(...args) {
                appendOutput(args.join(' '));
                originalConsole.log.apply(console, args);
            };

            console.error = function(...args) {
                appendOutput('ERROR: ' + args.join(' '));
                originalConsole.error.apply(console, args);
            };

            console.warn = function(...args) {
                appendOutput('WARN: ' + args.join(' '));
                originalConsole.warn.apply(console, args);
            };

            return originalConsole;
        }

        async function runTests() {
            if (isRunning) return;
            
            isRunning = true;
            const runButton = document.getElementById('runButton');
            runButton.disabled = true;
            runButton.textContent = '⏳ Running Tests...';
            
            clearOutput();
            updateStatus('🔄 Running EmailJS error handling tests...', 'running');
            
            const originalConsole = setupConsoleCapture();
            
            try {
                // Mock EmailJS for testing
                window.emailjs = {
                    send: function(serviceId, templateId, params, publicKey) {
                        // This will be overridden in individual tests
                        return Promise.resolve();
                    }
                };

                const success = await EmailJSErrorHandlingTests.runAllTests();
                
                if (success) {
                    updateStatus('✅ All tests passed! EmailJS error handling is working correctly.', 'success');
                } else {
                    updateStatus('❌ Some tests failed. Please review the implementation.', 'error');
                }
                
            } catch (error) {
                console.error('Test execution error:', error);
                updateStatus('❌ Test execution failed: ' + error.message, 'error');
            } finally {
                // Restore console
                console.log = originalConsole.log;
                console.error = originalConsole.error;
                console.warn = originalConsole.warn;
                
                isRunning = false;
                runButton.disabled = false;
                runButton.textContent = '🚀 Run All Tests';
            }
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            updateStatus('📋 Ready to run EmailJS error handling tests. Click "Run All Tests" to begin.', 'success');
        });
    </script>
</body>
</html>