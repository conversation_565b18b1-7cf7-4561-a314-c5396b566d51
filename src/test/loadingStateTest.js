/**
 * Loading State and User Experience Test Suite
 * 
 * This test suite verifies:
 * - Loading indicator appears during form submission
 * - Submit button disable functionality to prevent duplicate submissions
 * - Proper loading state cleanup after success or error
 * - User experience flow from submission to completion
 * 
 * Requirements: 1.2, 3.4, 4.4
 */

class LoadingStateTestSuite {
  constructor() {
    this.testResults = [];
    this.testContainer = null;
    this.contactForm = null;
    this.submitButton = null;
    this.originalEmailJS = null;
  }

  // Initialize test environment
  async initialize() {
    console.log('🚀 Initializing Loading State Test Suite...');
    
    // Create test container
    this.testContainer = document.createElement('div');
    this.testContainer.id = 'loading-test-container';
    this.testContainer.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 600px;
      max-height: 80vh;
      overflow-y: auto;
      background: white;
      border: 2px solid #e5e7eb;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      z-index: 10000;
    `;
    
    // Create simplified contact form for testing
    this.testContainer.innerHTML = `
      <h3 style="margin: 0 0 20px 0; color: #1f2937; font-size: 18px; font-weight: bold;">
        Loading State Test Form
      </h3>
      <form id="test-contact-form" style="display: flex; flex-direction: column; gap: 15px;">
        <input type="text" name="firstName" placeholder="First Name" required 
               style="padding: 10px; border: 1px solid #d1d5db; border-radius: 6px;">
        <input type="email" name="email" placeholder="Email" required 
               style="padding: 10px; border: 1px solid #d1d5db; border-radius: 6px;">
        <button type="submit" id="test-submit-btn" 
                style="padding: 12px; background: #f59e0b; color: white; border: none; border-radius: 6px; font-weight: bold; cursor: pointer;">
          <span class="btn-text">Send Message</span>
        </button>
      </form>
      <div id="test-results" style="margin-top: 20px; padding: 15px; background: #f9fafb; border-radius: 6px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
      <button onclick="window.loadingStateTests.cleanup()" 
              style="margin-top: 10px; padding: 8px 16px; background: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer;">
        Close Tests
      </button>
    `;
    
    document.body.appendChild(this.testContainer);
    
    this.contactForm = document.getElementById('test-contact-form');
    this.submitButton = document.getElementById('test-submit-btn');
    
    // Mock EmailJS for controlled testing
    this.setupEmailJSMock();
    
    // Setup form submission handler
    this.setupFormHandler();
    
    this.log('✅ Test environment initialized');
  }

  // Setup EmailJS mock for testing different scenarios
  setupEmailJSMock() {
    this.originalEmailJS = window.emailjs;
    
    window.emailjs = {
      send: (serviceId, templateId, params, publicKey) => {
        return new Promise((resolve, reject) => {
          // Simulate network delay
          setTimeout(() => {
            if (window.testScenario === 'success') {
              resolve({ status: 200, text: 'OK' });
            } else if (window.testScenario === 'error') {
              reject(new Error('Network error'));
            } else {
              // Default to success
              resolve({ status: 200, text: 'OK' });
            }
          }, 2000); // 2 second delay to test loading states
        });
      }
    };
  }

  // Setup form submission handler with loading states
  setupFormHandler() {
    let isSubmitting = false;
    
    this.contactForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      if (isSubmitting) {
        this.log('⚠️ Duplicate submission prevented');
        return;
      }
      
      // Start loading state
      isSubmitting = true;
      this.updateButtonState(true);
      
      try {
        // Simulate form submission
        const formData = new FormData(this.contactForm);
        const templateParams = {
          from_name: formData.get('firstName'),
          from_email: formData.get('email')
        };
        
        await window.emailjs.send('test_service', 'test_template', templateParams, 'test_key');
        
        this.log('✅ Form submitted successfully');
        this.contactForm.reset();
        
      } catch (error) {
        this.log('❌ Form submission failed: ' + error.message);
      } finally {
        // Clean up loading state
        isSubmitting = false;
        this.updateButtonState(false);
      }
    });
  }

  // Update button state for loading
  updateButtonState(loading) {
    const btnText = this.submitButton.querySelector('.btn-text');
    
    if (loading) {
      this.submitButton.disabled = true;
      this.submitButton.style.background = '#9ca3af';
      this.submitButton.style.cursor = 'not-allowed';
      btnText.textContent = 'Sending...';
    } else {
      this.submitButton.disabled = false;
      this.submitButton.style.background = '#f59e0b';
      this.submitButton.style.cursor = 'pointer';
      btnText.textContent = 'Send Message';
    }
  }

  // Test 1: Verify loading indicator appears during submission
  async testLoadingIndicatorAppears() {
    this.log('\n🧪 Test 1: Loading indicator appears during submission');
    
    window.testScenario = 'success';
    
    // Fill form
    this.contactForm.firstName.value = 'Test User';
    this.contactForm.email.value = '<EMAIL>';
    
    // Submit form
    const submitPromise = new Promise((resolve) => {
      this.contactForm.addEventListener('submit', () => {
        // Check loading state immediately after submission
        setTimeout(() => {
          const isDisabled = this.submitButton.disabled;
          const buttonText = this.submitButton.querySelector('.btn-text').textContent;
          const isLoadingStyle = this.submitButton.style.background === 'rgb(156, 163, 175)';
          
          if (isDisabled && buttonText === 'Sending...' && isLoadingStyle) {
            this.log('✅ Loading indicator appears correctly');
            this.recordTest('Loading Indicator Appears', true, 'Button disabled, text changed to "Sending...", style updated');
          } else {
            this.log('❌ Loading indicator not working properly');
            this.log(`  - Disabled: ${isDisabled}, Text: ${buttonText}, Style: ${isLoadingStyle}`);
            this.recordTest('Loading Indicator Appears', false, 'Loading state not properly applied');
          }
          resolve();
        }, 100);
      }, { once: true });
    });
    
    this.submitButton.click();
    await submitPromise;
  }

  // Test 2: Submit button disable functionality prevents duplicate submissions
  async testDuplicateSubmissionPrevention() {
    this.log('\n🧪 Test 2: Submit button prevents duplicate submissions');
    
    window.testScenario = 'success';
    
    // Fill form
    this.contactForm.firstName.value = 'Test User';
    this.contactForm.email.value = '<EMAIL>';
    
    let submissionCount = 0;
    const originalHandler = this.contactForm.onsubmit;
    
    // Count submissions
    this.contactForm.addEventListener('submit', () => {
      submissionCount++;
    });
    
    // Rapid fire clicks
    this.submitButton.click();
    setTimeout(() => this.submitButton.click(), 50);
    setTimeout(() => this.submitButton.click(), 100);
    setTimeout(() => this.submitButton.click(), 150);
    
    // Wait and check
    await new Promise(resolve => setTimeout(resolve, 300));
    
    if (submissionCount === 1) {
      this.log('✅ Duplicate submissions prevented successfully');
      this.recordTest('Duplicate Submission Prevention', true, 'Only one submission processed despite multiple clicks');
    } else {
      this.log(`❌ Duplicate submissions not prevented (${submissionCount} submissions)`);
      this.recordTest('Duplicate Submission Prevention', false, `${submissionCount} submissions processed`);
    }
  }

  // Test 3: Loading state cleanup after success
  async testLoadingStateCleanupSuccess() {
    this.log('\n🧪 Test 3: Loading state cleanup after successful submission');
    
    window.testScenario = 'success';
    
    // Fill form
    this.contactForm.firstName.value = 'Test User';
    this.contactForm.email.value = '<EMAIL>';
    
    // Submit and wait for completion
    this.submitButton.click();
    
    // Wait for submission to complete
    await new Promise(resolve => setTimeout(resolve, 2500));
    
    // Check if loading state is cleaned up
    const isEnabled = !this.submitButton.disabled;
    const buttonText = this.submitButton.querySelector('.btn-text').textContent;
    const isNormalStyle = this.submitButton.style.background === 'rgb(245, 158, 11)';
    
    if (isEnabled && buttonText === 'Send Message' && isNormalStyle) {
      this.log('✅ Loading state cleaned up after success');
      this.recordTest('Loading State Cleanup (Success)', true, 'Button re-enabled, text reset, style restored');
    } else {
      this.log('❌ Loading state not properly cleaned up after success');
      this.log(`  - Enabled: ${isEnabled}, Text: ${buttonText}, Style: ${isNormalStyle}`);
      this.recordTest('Loading State Cleanup (Success)', false, 'Loading state persisted after success');
    }
  }

  // Test 4: Loading state cleanup after error
  async testLoadingStateCleanupError() {
    this.log('\n🧪 Test 4: Loading state cleanup after error');
    
    window.testScenario = 'error';
    
    // Fill form
    this.contactForm.firstName.value = 'Test User';
    this.contactForm.email.value = '<EMAIL>';
    
    // Submit and wait for completion
    this.submitButton.click();
    
    // Wait for submission to complete (with error)
    await new Promise(resolve => setTimeout(resolve, 2500));
    
    // Check if loading state is cleaned up
    const isEnabled = !this.submitButton.disabled;
    const buttonText = this.submitButton.querySelector('.btn-text').textContent;
    const isNormalStyle = this.submitButton.style.background === 'rgb(245, 158, 11)';
    
    if (isEnabled && buttonText === 'Send Message' && isNormalStyle) {
      this.log('✅ Loading state cleaned up after error');
      this.recordTest('Loading State Cleanup (Error)', true, 'Button re-enabled, text reset, style restored');
    } else {
      this.log('❌ Loading state not properly cleaned up after error');
      this.log(`  - Enabled: ${isEnabled}, Text: ${buttonText}, Style: ${isNormalStyle}`);
      this.recordTest('Loading State Cleanup (Error)', false, 'Loading state persisted after error');
    }
  }

  // Test 5: Complete user experience flow
  async testCompleteUserExperienceFlow() {
    this.log('\n🧪 Test 5: Complete user experience flow');
    
    window.testScenario = 'success';
    
    const flowSteps = [];
    
    // Step 1: Form is ready
    flowSteps.push({
      step: 'Initial State',
      passed: !this.submitButton.disabled && this.submitButton.querySelector('.btn-text').textContent === 'Send Message'
    });
    
    // Fill form
    this.contactForm.firstName.value = 'Test User';
    this.contactForm.email.value = '<EMAIL>';
    
    // Step 2: Submit form
    this.submitButton.click();
    
    // Check immediate loading state
    setTimeout(() => {
      flowSteps.push({
        step: 'Loading State Active',
        passed: this.submitButton.disabled && this.submitButton.querySelector('.btn-text').textContent === 'Sending...'
      });
    }, 100);
    
    // Wait for completion and check final state
    await new Promise(resolve => setTimeout(resolve, 2500));
    
    flowSteps.push({
      step: 'Final State',
      passed: !this.submitButton.disabled && this.submitButton.querySelector('.btn-text').textContent === 'Send Message'
    });
    
    const allStepsPassed = flowSteps.every(step => step.passed);
    
    if (allStepsPassed) {
      this.log('✅ Complete user experience flow working correctly');
      this.recordTest('Complete UX Flow', true, 'All flow steps completed successfully');
    } else {
      this.log('❌ User experience flow has issues');
      flowSteps.forEach(step => {
        this.log(`  - ${step.step}: ${step.passed ? '✅' : '❌'}`);
      });
      this.recordTest('Complete UX Flow', false, 'Some flow steps failed');
    }
  }

  // Run all tests
  async runAllTests() {
    this.log('🎯 Starting Loading State and User Experience Tests\n');
    
    try {
      await this.testLoadingIndicatorAppears();
      await new Promise(resolve => setTimeout(resolve, 3000)); // Wait between tests
      
      await this.testDuplicateSubmissionPrevention();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      await this.testLoadingStateCleanupSuccess();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      await this.testLoadingStateCleanupError();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      await this.testCompleteUserExperienceFlow();
      
      this.generateSummary();
      
    } catch (error) {
      this.log('❌ Test execution failed: ' + error.message);
    }
  }

  // Record test result
  recordTest(testName, passed, details) {
    this.testResults.push({
      name: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
  }

  // Generate test summary
  generateSummary() {
    const passedTests = this.testResults.filter(test => test.passed).length;
    const totalTests = this.testResults.length;
    
    this.log(`\n📊 TEST SUMMARY`);
    this.log(`Total Tests: ${totalTests}`);
    this.log(`Passed: ${passedTests}`);
    this.log(`Failed: ${totalTests - passedTests}`);
    this.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    this.log('\n📋 DETAILED RESULTS:');
    this.testResults.forEach(test => {
      this.log(`${test.passed ? '✅' : '❌'} ${test.name}: ${test.details}`);
    });
    
    if (passedTests === totalTests) {
      this.log('\n🎉 All loading state and user experience tests passed!');
    } else {
      this.log('\n⚠️ Some tests failed. Please review the implementation.');
    }
  }

  // Utility method to log messages
  log(message) {
    console.log(message);
    const resultsDiv = document.getElementById('test-results');
    if (resultsDiv) {
      resultsDiv.innerHTML += message + '\n';
      resultsDiv.scrollTop = resultsDiv.scrollHeight;
    }
  }

  // Cleanup test environment
  cleanup() {
    if (this.testContainer) {
      document.body.removeChild(this.testContainer);
    }
    
    // Restore original EmailJS
    if (this.originalEmailJS) {
      window.emailjs = this.originalEmailJS;
    }
    
    // Clean up global variables
    delete window.testScenario;
    delete window.loadingStateTests;
    
    console.log('🧹 Test environment cleaned up');
  }
}

// Initialize and run tests
window.loadingStateTests = new LoadingStateTestSuite();