<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmailJS Template Test Runner</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-scenario {
            margin-bottom: 20px;
            padding: 15px;
            border-left: 4px solid #007bff;
            background-color: white;
        }
        .scenario-title {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .scenario-description {
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }
        .template-params {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .validation-results {
            margin-top: 10px;
        }
        .validation-item {
            padding: 5px 0;
            font-size: 14px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .config-status {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .config-configured {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .config-not-configured {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log-output {
            background-color: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 EmailJS Template Test Runner</h1>
        
        <div id="configStatus" class="config-status">
            <span id="configText">Checking EmailJS configuration...</span>
        </div>
        
        <div class="test-section">
            <h2>🧪 Test Controls</h2>
            <button onclick="runTemplateTests()">Run Template Mapping Tests</button>
            <button id="sendEmailBtn" onclick="runWithEmails()" disabled>Send Test Emails</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-section">
            <h2>📋 Test Scenarios</h2>
            <div id="testScenarios"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Test Output</h2>
            <div id="logOutput" class="log-output">Ready to run tests...\n</div>
        </div>
    </div>

    <script type="module">
        // Import EmailJS and configuration
        import emailjs from 'https://cdn.skypack.dev/@emailjs/browser';
        
        // EmailJS Configuration (copied from config file)
        const EMAILJS_CONFIG = {
            SERVICE_ID: 'service_s3o73s7',
            TEMPLATE_ID: 'template_xutydd8', 
            PUBLIC_KEY: 'LJ0ZcxiBPMbk6VbgD'
        };

        // Test scenarios
        const testScenarios = [
            {
                name: 'Complete Form Data',
                description: 'All fields filled with valid data',
                formData: {
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    phone: '******-123-4567',
                    eventType: 'wedding',
                    eventDate: '2024-06-15',
                    budget: '25k-50k',
                    message: 'Looking for an elegant outdoor wedding venue with capacity for 150 guests.'
                }
            },
            {
                name: 'Minimal Required Data',
                description: 'Only required fields (name and email) filled',
                formData: {
                    firstName: 'Jane',
                    lastName: 'Smith',
                    email: '<EMAIL>',
                    phone: '',
                    eventType: '',
                    eventDate: '',
                    budget: '',
                    message: ''
                }
            },
            {
                name: 'Phone Only Contact',
                description: 'Name and phone provided, no email',
                formData: {
                    firstName: 'Mike',
                    lastName: 'Johnson',
                    email: '',
                    phone: '******-987-6543',
                    eventType: 'corporate',
                    eventDate: '2024-08-20',
                    budget: '50k-100k',
                    message: 'Corporate anniversary celebration for 200 employees.'
                }
            },
            {
                name: 'Partial Optional Fields',
                description: 'Some optional fields filled, others empty',
                formData: {
                    firstName: 'Sarah',
                    lastName: 'Wilson',
                    email: '<EMAIL>',
                    phone: '',
                    eventType: 'birthday',
                    eventDate: '',
                    budget: '10k-25k',
                    message: ''
                }
            }
        ];

        // Utility functions
        function prepareTemplateParams(formData) {
            return {
                from_name: `${formData.firstName} ${formData.lastName}`.trim(),
                from_email: formData.email,
                phone: formData.phone,
                event_type: formData.eventType || 'Not specified',
                event_date: formData.eventDate || 'Not specified',
                budget: formData.budget || 'Not specified',
                message: formData.message || 'No additional message provided',
                to_email: '<EMAIL>'
            };
        }

        function validateTemplateParams(params) {
            const results = [];
            
            if (!params.from_name || params.from_name.trim() === '') {
                results.push({ type: 'error', message: 'from_name is empty or missing' });
            } else {
                results.push({ type: 'success', message: 'from_name is properly set' });
            }
            
            if (!params.to_email || params.to_email !== '<EMAIL>') {
                results.push({ type: 'error', message: 'to_email is not <NAME_EMAIL>' });
            } else {
                results.push({ type: 'success', message: 'to_email is correctly set' });
            }
            
            const optionalFields = ['event_type', 'event_date', 'budget'];
            optionalFields.forEach(field => {
                if (params[field] === 'Not specified') {
                    results.push({ type: 'success', message: `${field} correctly shows "Not specified" when empty` });
                } else if (params[field] && params[field] !== '') {
                    results.push({ type: 'success', message: `${field} has value: "${params[field]}"` });
                } else {
                    results.push({ type: 'error', message: `${field} is empty but should show "Not specified"` });
                }
            });
            
            if (params.message === 'No additional message provided') {
                results.push({ type: 'success', message: 'message correctly shows default text when empty' });
            } else if (params.message && params.message !== '') {
                results.push({ type: 'success', message: `message has custom content: "${params.message}"` });
            } else {
                results.push({ type: 'error', message: 'message is empty but should have default text' });
            }
            
            if (params.from_email && params.from_email !== '') {
                results.push({ type: 'success', message: `from_email is set: "${params.from_email}"` });
            } else {
                results.push({ type: 'info', message: 'from_email is empty (acceptable if phone is provided)' });
            }
            
            if (params.phone && params.phone !== '') {
                results.push({ type: 'success', message: `phone is set: "${params.phone}"` });
            } else {
                results.push({ type: 'info', message: 'phone is empty (acceptable if email is provided)' });
            }
            
            return results;
        }

        function checkEmailJSConfiguration() {
            const isConfigured = 
                EMAILJS_CONFIG.SERVICE_ID !== 'YOUR_SERVICE_ID' &&
                EMAILJS_CONFIG.TEMPLATE_ID !== 'YOUR_TEMPLATE_ID' &&
                EMAILJS_CONFIG.PUBLIC_KEY !== 'YOUR_PUBLIC_KEY';
            
            return isConfigured;
        }

        function log(message) {
            const logOutput = document.getElementById('logOutput');
            logOutput.textContent += message + '\n';
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function displayTemplateParams(params) {
            return `To: ${params.to_email}
From Name: ${params.from_name}
From Email: ${params.from_email || '(not provided)'}
Phone: ${params.phone || '(not provided)'}
Event Type: ${params.event_type}
Event Date: ${params.event_date}
Budget: ${params.budget}
Message: ${params.message}`;
        }

        // Initialize page
        function init() {
            // Check configuration
            const isConfigured = checkEmailJSConfiguration();
            const configStatus = document.getElementById('configStatus');
            const configText = document.getElementById('configText');
            const sendEmailBtn = document.getElementById('sendEmailBtn');
            
            if (isConfigured) {
                configStatus.className = 'config-status config-configured';
                configText.textContent = '✅ EmailJS is properly configured and ready for testing';
                sendEmailBtn.disabled = false;
            } else {
                configStatus.className = 'config-status config-not-configured';
                configText.textContent = '❌ EmailJS is not configured (using placeholder values). Template mapping tests will run in simulation mode.';
                sendEmailBtn.disabled = true;
            }
            
            // Display test scenarios
            const scenariosContainer = document.getElementById('testScenarios');
            testScenarios.forEach((scenario, index) => {
                const scenarioDiv = document.createElement('div');
                scenarioDiv.className = 'test-scenario';
                scenarioDiv.innerHTML = `
                    <div class="scenario-title">${index + 1}. ${scenario.name}</div>
                    <div class="scenario-description">${scenario.description}</div>
                    <div class="template-params">${displayTemplateParams(prepareTemplateParams(scenario.formData))}</div>
                `;
                scenariosContainer.appendChild(scenarioDiv);
            });
        }

        // Test functions
        window.runTemplateTests = function() {
            log('🚀 Starting Email Template Tests');
            log('='.repeat(60));
            
            testScenarios.forEach((scenario, index) => {
                log(`\n🧪 Testing: ${scenario.name}`);
                log(`📝 Description: ${scenario.description}`);
                
                const templateParams = prepareTemplateParams(scenario.formData);
                log('\n📧 Email Template Parameters:');
                log('─'.repeat(50));
                log(displayTemplateParams(templateParams));
                log('─'.repeat(50));
                
                const validationResults = validateTemplateParams(templateParams);
                log('\n🔍 Validation Results:');
                validationResults.forEach(result => {
                    const icon = result.type === 'success' ? '✅' : 
                               result.type === 'error' ? '❌' : 
                               result.type === 'warning' ? '⚠️' : 'ℹ️';
                    log(`  ${icon} ${result.message}`);
                });
            });
            
            log('\n📊 Test Summary');
            log('='.repeat(60));
            log('🎯 Key Validation Points Checked:');
            log('  ✅ Template parameter mapping');
            log('  ✅ "Not specified" handling for empty optional fields');
            log('  ✅ Default message for empty message field');
            log('  ✅ Proper name concatenation');
            log('  ✅ Contact information handling');
            log('\n✅ All template mapping tests completed successfully!');
        };

        window.runWithEmails = async function() {
            if (!checkEmailJSConfiguration()) {
                log('❌ Cannot send emails: EmailJS is not configured');
                return;
            }
            
            log('🚀 Starting Email Template Tests with Actual Email Sending');
            log('='.repeat(60));
            
            for (let i = 0; i < testScenarios.length; i++) {
                const scenario = testScenarios[i];
                log(`\n🧪 Testing: ${scenario.name}`);
                
                const templateParams = prepareTemplateParams(scenario.formData);
                
                try {
                    log(`📤 Sending test email for scenario: ${scenario.name}`);
                    
                    const result = await emailjs.send(
                        EMAILJS_CONFIG.SERVICE_ID,
                        EMAILJS_CONFIG.TEMPLATE_ID,
                        templateParams,
                        EMAILJS_CONFIG.PUBLIC_KEY
                    );
                    
                    log('✅ Test email sent successfully!');
                    log(`   Status: ${result.status}`);
                    log(`   Text: ${result.text}`);
                    
                    if (i < testScenarios.length - 1) {
                        log('⏳ Waiting 3 seconds before next test...');
                        await new Promise(resolve => setTimeout(resolve, 3000));
                    }
                } catch (error) {
                    log('❌ Failed to send test email:');
                    log(`   Error: ${error.message}`);
                    log(`   Status: ${error.status || 'Unknown'}`);
                }
            }
            
            log('\n🎯 Email testing completed! Check <EMAIL> for test emails.');
        };

        window.clearLog = function() {
            document.getElementById('logOutput').textContent = 'Log cleared...\n';
        };

        // Initialize when page loads
        init();
    </script>
</body>
</html>