#!/usr/bin/env node

/**
 * Node.js Test Runner for Form Validation Tests
 * Task 5: Test form validation and error handling scenarios
 * 
 * This script runs the form validation tests and generates a summary report.
 */

import FormValidationTester from './formValidationTest.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function runTests() {
    console.log('🧪 Form Validation Test Suite - Node.js Runner');
    console.log('=' .repeat(60));
    console.log('Task 5: Test form validation and error handling scenarios');
    console.log('Testing Contact form validation logic and error handling');
    console.log('');

    try {
        // Create and run the test suite
        const tester = new FormValidationTester();
        const results = tester.runAllTests();
        
        // Generate detailed report
        const report = generateDetailedReport(results);
        
        // Save report to file
        const reportPath = path.join(__dirname, 'formValidationTestSummary.md');
        fs.writeFileSync(reportPath, report);
        
        console.log(`\n📄 Detailed report saved to: ${reportPath}`);
        
        // Exit with appropriate code
        process.exit(results.failed > 0 ? 1 : 0);
        
    } catch (error) {
        console.error('❌ Test execution failed:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

function generateDetailedReport(results) {
    const timestamp = new Date().toISOString();
    
    return `# Form Validation Test Report

**Generated:** ${timestamp}  
**Task:** 5. Test form validation and error handling scenarios  
**Component:** Contact.tsx form validation logic

## Test Summary

- **Total Tests:** ${results.totalTests}
- **Passed:** ${results.passed} ✅
- **Failed:** ${results.failed} ❌
- **Success Rate:** ${results.successRate.toFixed(1)}%

## Requirements Coverage

This test suite validates the following requirements:

- **Requirement 1.3:** ✅ Form validation for invalid email and phone formats
- **Requirement 1.4:** ✅ Clear error messages for validation failures  
- **Requirement 4.3:** ✅ Field highlighting and error correction guidance

## Test Categories

### 1. Invalid Email Format Validation
Tests that various invalid email formats are properly rejected by the validation logic.

### 2. Invalid Phone Format Validation  
Tests that various invalid phone formats are properly rejected by the validation logic.

### 3. Valid Format Acceptance
Ensures that valid email and phone formats are correctly accepted.

### 4. Missing Contact Information
Tests scenarios where neither email nor phone contact information is provided.

### 5. Error Message Clarity
Verifies that error messages are clear, specific, and actionable for users.

### 6. Field Highlighting Logic
Tests that error states are properly flagged for UI field highlighting.

### 7. Edge Cases and Boundary Conditions
Tests special cases like whitespace-only inputs and international formats.

## Detailed Test Results

${results.results.map(result => `
### ${result.test}
- **Status:** ${result.passed ? '✅ PASSED' : '❌ FAILED'}
- **Details:** ${result.details}
- **Timestamp:** ${result.timestamp}
`).join('\n')}

## Validation Logic Analysis

The Contact component implements the following validation rules:

1. **Email Validation:** Uses regex pattern \`/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\`
2. **Phone Validation:** Uses regex pattern \`/^[\\+]?[1-9][\\d]{0,15}$/\` (after removing formatting)
3. **Contact Requirement:** At least one valid contact method (email OR phone) must be provided
4. **Error Prioritization:** Shows specific field errors when possible, general contact error when both are missing

## Error Message Mapping

- **Missing Contact:** "Please provide either a valid email address or phone number - both are mandatory for us to contact you back."
- **Invalid Email:** "Please provide a valid email address."
- **Invalid Phone:** "Please provide a valid phone number."

## Field Highlighting Implementation

The validation logic returns an errors object with keys corresponding to form fields:
- \`errors.contact\` - General contact requirement error
- \`errors.email\` - Email-specific validation error
- \`errors.phone\` - Phone-specific validation error

These error keys are used in the UI to apply CSS classes for field highlighting:
- Error fields get \`border-red-300 bg-red-50\` classes
- Normal fields get \`border-gray-300\` classes

## Recommendations

${results.failed > 0 ? `
### ❌ Issues Found
${results.results.filter(r => !r.passed).map(r => `- **${r.test}:** ${r.details}`).join('\n')}

### Suggested Fixes
Review the validation logic in Contact.tsx to address the failed test cases above.
` : `
### ✅ All Tests Passed
The form validation logic is working correctly and meets all specified requirements.
`}

## Test Execution Details

- **Test Runner:** Node.js
- **Test File:** formValidationTest.js
- **Component Under Test:** src/components/Contact.tsx
- **Validation Functions:** validateEmail(), validatePhone(), form validation logic

---
*This report was automatically generated by the Form Validation Test Suite*
`;
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests();
}

export { runTests, generateDetailedReport };