<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>End-to-End Integration Test Runner - EmailJS Contact Form</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        .btn-primary:hover {
            background-color: #45a049;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: #2196F3;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #1976D2;
            transform: translateY(-2px);
        }
        .btn-danger {
            background-color: #f44336;
            color: white;
        }
        .btn-danger:hover {
            background-color: #d32f2f;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 600;
        }
        .status.running {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .log-container {
            background-color: #1e1e1e;
            color: #ffffff;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 500px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #333;
        }
        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
        }
        .log-entry.success {
            color: #4CAF50;
        }
        .log-entry.error {
            color: #f44336;
        }
        .log-entry.info {
            color: #2196F3;
        }
        .log-entry.warning {
            color: #ff9800;
        }
        .results-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
            border-left: 4px solid #4CAF50;
        }
        .metric-card.failed {
            border-left-color: #f44336;
        }
        .metric-card.warning {
            border-left-color: #ff9800;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
        .test-details {
            margin-top: 30px;
        }
        .test-item {
            background: #f9f9f9;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid #4CAF50;
        }
        .test-item.failed {
            border-left-color: #f44336;
            background: #fef5f5;
        }
        .test-item.error {
            border-left-color: #ff9800;
            background: #fff8f0;
        }
        .test-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .test-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .test-status.pass {
            background-color: #4CAF50;
            color: white;
        }
        .test-status.fail {
            background-color: #f44336;
            color: white;
        }
        .test-status.error {
            background-color: #ff9800;
            color: white;
        }
        .email-details {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid #b3d9ff;
        }
        .email-params {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }
        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 End-to-End Integration Test Runner</h1>
        <p>EmailJS Contact Form - Complete User Journey Testing</p>
    </div>

    <div class="test-container">
        <div class="warning-box">
            <strong>⚠️ Important:</strong> This test will send real <NAME_EMAIL>. 
            Make sure EmailJS is properly configured before running tests.
        </div>

        <div class="info-box">
            <strong>📋 Test Coverage:</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Complete user journey from form fill to email receipt</li>
                <li>Email formatting and professional appearance verification</li>
                <li>Multiple form submissions for consistency testing</li>
                <li>Console error monitoring during operation</li>
                <li>All form validation scenarios</li>
            </ul>
        </div>

        <div class="controls">
            <button id="runTests" class="btn btn-primary">🚀 Run All Tests</button>
            <button id="clearLogs" class="btn btn-secondary">🗑️ Clear Logs</button>
            <button id="exportResults" class="btn btn-secondary" disabled>📊 Export Results</button>
        </div>

        <div id="status" class="status" style="display: none;"></div>
        
        <div class="progress-bar" id="progressContainer" style="display: none;">
            <div class="progress-fill" id="progressBar"></div>
        </div>

        <div id="results-summary" class="results-summary" style="display: none;"></div>

        <div class="log-container" id="logContainer">
            <div class="log-entry info">Ready to run end-to-end integration tests...</div>
            <div class="log-entry info">Click "Run All Tests" to begin comprehensive testing</div>
        </div>

        <div id="testDetails" class="test-details" style="display: none;"></div>
    </div>

    <!-- EmailJS SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    
    <!-- Test Implementation -->
    <script src="endToEndIntegrationTest.js"></script>

    <script>
        let testRunner = null;
        let testResults = null;

        // Initialize EmailJS
        emailjs.init('LJ0ZcxiBPMbk6VbgD');

        // DOM Elements
        const runTestsBtn = document.getElementById('runTests');
        const clearLogsBtn = document.getElementById('clearLogs');
        const exportResultsBtn = document.getElementById('exportResults');
        const statusDiv = document.getElementById('status');
        const logContainer = document.getElementById('logContainer');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const resultsSummary = document.getElementById('results-summary');
        const testDetails = document.getElementById('testDetails');

        // Event Listeners
        runTestsBtn.addEventListener('click', runTests);
        clearLogsBtn.addEventListener('click', clearLogs);
        exportResultsBtn.addEventListener('click', exportResults);

        // Override console methods to capture logs
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        function addLogEntry(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // Also log to original console
            originalConsole[type === 'error' ? 'error' : 'log'](message);
        }

        // Override console methods
        console.log = (...args) => addLogEntry(args.join(' '), 'info');
        console.error = (...args) => addLogEntry(args.join(' '), 'error');
        console.warn = (...args) => addLogEntry(args.join(' '), 'warning');
        console.info = (...args) => addLogEntry(args.join(' '), 'info');

        async function runTests() {
            try {
                // Reset UI
                runTestsBtn.disabled = true;
                exportResultsBtn.disabled = true;
                statusDiv.style.display = 'block';
                statusDiv.className = 'status running';
                statusDiv.textContent = '🔄 Running end-to-end integration tests...';
                
                progressContainer.style.display = 'block';
                progressBar.style.width = '0%';
                
                resultsSummary.style.display = 'none';
                testDetails.style.display = 'none';

                addLogEntry('Starting end-to-end integration test suite...', 'info');

                // Create test runner instance
                testRunner = new EndToEndIntegrationTest();

                // Simulate progress updates
                const progressSteps = [
                    { step: 'Initializing test environment...', progress: 10 },
                    { step: 'Testing complete user journey...', progress: 25 },
                    { step: 'Testing minimal valid submission...', progress: 40 },
                    { step: 'Testing phone-only submission...', progress: 55 },
                    { step: 'Testing multiple submissions...', progress: 70 },
                    { step: 'Verifying email formatting...', progress: 85 },
                    { step: 'Checking console errors...', progress: 95 },
                    { step: 'Generating test report...', progress: 100 }
                ];

                let currentStep = 0;
                const progressInterval = setInterval(() => {
                    if (currentStep < progressSteps.length) {
                        const step = progressSteps[currentStep];
                        addLogEntry(step.step, 'info');
                        progressBar.style.width = step.progress + '%';
                        currentStep++;
                    } else {
                        clearInterval(progressInterval);
                    }
                }, 2000);

                // Run all tests
                testResults = await testRunner.runAllTests();

                clearInterval(progressInterval);
                progressBar.style.width = '100%';

                // Update UI with results
                if (testResults.error) {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ Critical error during testing: ' + testResults.message;
                    addLogEntry('Critical error: ' + testResults.message, 'error');
                } else {
                    const allPassed = testResults.summary.passedTests === testResults.summary.totalTests &&
                                    testResults.emailDelivery.successfulDeliveries === testResults.emailDelivery.totalEmails &&
                                    testResults.consoleErrors.errorCount === 0;

                    if (allPassed) {
                        statusDiv.className = 'status success';
                        statusDiv.textContent = '🎉 All tests passed! End-to-end integration is working correctly.';
                        addLogEntry('All tests completed successfully!', 'success');
                    } else {
                        statusDiv.className = 'status error';
                        statusDiv.textContent = '⚠️ Some tests failed. Please review the detailed results below.';
                        addLogEntry('Some tests failed. Check detailed results.', 'warning');
                    }

                    displayResults(testResults);
                }

                exportResultsBtn.disabled = false;

            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Unexpected error: ' + error.message;
                addLogEntry('Unexpected error: ' + error.message, 'error');
            } finally {
                runTestsBtn.disabled = false;
                progressContainer.style.display = 'none';
            }
        }

        function displayResults(results) {
            // Display summary metrics
            resultsSummary.style.display = 'grid';
            resultsSummary.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${results.summary.totalTests}</div>
                    <div class="metric-label">Total Tests</div>
                </div>
                <div class="metric-card ${results.summary.failedTests > 0 ? 'failed' : ''}">
                    <div class="metric-value">${results.summary.passedTests}</div>
                    <div class="metric-label">Passed</div>
                </div>
                <div class="metric-card ${results.summary.failedTests > 0 ? 'failed' : ''}">
                    <div class="metric-value">${results.summary.failedTests}</div>
                    <div class="metric-label">Failed</div>
                </div>
                <div class="metric-card ${results.summary.errorTests > 0 ? 'warning' : ''}">
                    <div class="metric-value">${results.summary.errorTests}</div>
                    <div class="metric-label">Errors</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${results.summary.successRate}</div>
                    <div class="metric-label">Success Rate</div>
                </div>
                <div class="metric-card ${results.emailDelivery.failedDeliveries > 0 ? 'failed' : ''}">
                    <div class="metric-value">${results.emailDelivery.successfulDeliveries}/${results.emailDelivery.totalEmails}</div>
                    <div class="metric-label">Email Deliveries</div>
                </div>
            `;

            // Display detailed test results
            testDetails.style.display = 'block';
            let detailsHTML = '<h3>📋 Detailed Test Results</h3>';

            results.detailedResults.forEach(test => {
                const statusClass = test.status.toLowerCase();
                detailsHTML += `
                    <div class="test-item ${statusClass === 'fail' ? 'failed' : statusClass === 'error' ? 'error' : ''}">
                        <div class="test-name">
                            ${test.test}
                            <span class="test-status ${statusClass}">${test.status}</span>
                        </div>
                        <div style="margin: 10px 0; color: #666;">
                            ${test.details}
                        </div>
                        ${test.timestamp ? `<div style="font-size: 12px; color: #999;">Completed: ${new Date(test.timestamp).toLocaleString()}</div>` : ''}
                    </div>
                `;
            });

            // Display email delivery details
            if (results.emailResults && results.emailResults.length > 0) {
                detailsHTML += '<h3>📧 Email Delivery Details</h3>';
                results.emailResults.forEach((email, index) => {
                    detailsHTML += `
                        <div class="email-details">
                            <strong>Email ${index + 1}:</strong> 
                            <span class="test-status ${email.success ? 'pass' : 'fail'}">${email.success ? 'DELIVERED' : 'FAILED'}</span>
                            <div class="email-params">
                                <strong>Template Parameters:</strong><br>
                                ${JSON.stringify(email.templateParams, null, 2)}
                            </div>
                            ${email.error ? `<div style="color: #f44336; margin-top: 10px;"><strong>Error:</strong> ${email.error.message || email.error}</div>` : ''}
                        </div>
                    `;
                });
            }

            // Display console errors if any
            if (results.consoleErrors.errorCount > 0) {
                detailsHTML += '<h3>⚠️ Console Errors</h3>';
                results.consoleErrors.errors.forEach(error => {
                    detailsHTML += `<div class="test-item failed"><div style="font-family: monospace;">${error}</div></div>`;
                });
            }

            testDetails.innerHTML = detailsHTML;
        }

        function clearLogs() {
            logContainer.innerHTML = '<div class="log-entry info">Logs cleared. Ready for new test run...</div>';
            resultsSummary.style.display = 'none';
            testDetails.style.display = 'none';
            statusDiv.style.display = 'none';
            progressContainer.style.display = 'none';
            exportResultsBtn.disabled = true;
        }

        function exportResults() {
            if (!testResults) {
                addLogEntry('No test results to export', 'warning');
                return;
            }

            const dataStr = JSON.stringify(testResults, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `end-to-end-test-results-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            addLogEntry('Test results exported successfully', 'success');
        }

        // Initial log
        addLogEntry('End-to-end integration test runner initialized', 'info');
        addLogEntry('EmailJS configuration loaded', 'info');
    </script>
</body>
</html>