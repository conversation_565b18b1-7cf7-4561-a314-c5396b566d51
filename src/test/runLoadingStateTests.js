#!/usr/bin/env node

/**
 * Loading State Tests Runner
 * 
 * This script runs automated tests for loading states and user experience
 * during form submission using Puppeteer for browser automation.
 * 
 * Requirements tested: 1.2, 3.4, 4.4
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class LoadingStateTestRunner {
  constructor() {
    this.testResults = [];
    this.puppeteer = null;
    this.browser = null;
    this.page = null;
  }

  async initialize() {
    console.log('🚀 Initializing Loading State Test Runner...');
    
    try {
      // Try to import Puppeteer
      const puppeteerModule = await import('puppeteer');
      this.puppeteer = puppeteerModule.default;
    } catch (error) {
      console.log('⚠️ Puppeteer not available. Running simplified tests...');
      return this.runSimplifiedTests();
    }
    
    // Launch browser
    this.browser = await this.puppeteer.launch({
      headless: false, // Set to true for CI/CD
      devtools: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Set viewport
    await this.page.setViewport({ width: 1200, height: 800 });
    
    console.log('✅ Browser initialized');
  }

  async runSimplifiedTests() {
    console.log('\n🧪 Running Simplified Loading State Tests (No Browser)');
    console.log('=' .repeat(60));
    
    // Test 1: Verify test files exist
    const testFiles = [
      'src/test/loadingStateTest.js',
      'src/test/loadingStateTestRunner.html'
    ];
    
    let filesExist = true;
    testFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`✅ ${file} exists`);
      } else {
        console.log(`❌ ${file} missing`);
        filesExist = false;
      }
    });
    
    // Test 2: Verify Contact component has loading state implementation
    const contactFile = 'src/components/Contact.tsx';
    if (fs.existsSync(contactFile)) {
      const contactContent = fs.readFileSync(contactFile, 'utf8');
      
      const hasIsSubmitting = contactContent.includes('isSubmitting');
      const hasButtonDisable = contactContent.includes('disabled={isSubmitting}');
      const hasLoadingText = contactContent.includes('Sending...');
      const hasFinallyBlock = contactContent.includes('.finally(');
      
      console.log(`✅ Contact.tsx exists`);
      console.log(`${hasIsSubmitting ? '✅' : '❌'} Has isSubmitting state`);
      console.log(`${hasButtonDisable ? '✅' : '❌'} Has button disable functionality`);
      console.log(`${hasLoadingText ? '✅' : '❌'} Has loading text ("Sending...")`);
      console.log(`${hasFinallyBlock ? '✅' : '❌'} Has proper cleanup in finally block`);
      
      const implementationScore = [hasIsSubmitting, hasButtonDisable, hasLoadingText, hasFinallyBlock]
        .filter(Boolean).length;
      
      console.log(`\n📊 Implementation Score: ${implementationScore}/4 (${(implementationScore/4*100).toFixed(1)}%)`);
      
      if (implementationScore === 4) {
        console.log('🎉 All loading state features implemented correctly!');
      } else {
        console.log('⚠️ Some loading state features may need attention.');
      }
    } else {
      console.log(`❌ ${contactFile} not found`);
    }
    
    // Test 3: Manual test instructions
    console.log('\n📋 Manual Testing Instructions:');
    console.log('1. Open src/test/loadingStateTestRunner.html in a browser');
    console.log('2. Click "Run Loading State Tests" button');
    console.log('3. Observe the test form and verify:');
    console.log('   - Loading indicator appears during submission');
    console.log('   - Submit button is disabled during loading');
    console.log('   - Button text changes to "Sending..."');
    console.log('   - Loading state is cleaned up after completion');
    console.log('   - Duplicate submissions are prevented');
    
    this.generateSimplifiedSummary();
  }

  async runBrowserTests() {
    console.log('\n🧪 Running Browser-Based Loading State Tests');
    console.log('=' .repeat(60));
    
    try {
      // Load the test runner page
      const testRunnerPath = path.resolve('src/test/loadingStateTestRunner.html');
      await this.page.goto(`file://${testRunnerPath}`);
      
      console.log('📄 Test runner page loaded');
      
      // Wait for page to be ready
      await this.page.waitForSelector('#runTests');
      
      // Start tests
      await this.page.click('#runTests');
      console.log('🚀 Tests started');
      
      // Wait for tests to complete (they take about 15 seconds)
      await this.page.waitForTimeout(20000);
      
      // Get test results from console
      const consoleOutput = await this.page.evaluate(() => {
        const consoleDiv = document.getElementById('console');
        return consoleDiv ? consoleDiv.textContent : '';
      });
      
      console.log('\n📊 Test Results:');
      console.log(consoleOutput);
      
      // Check if tests passed
      const testsPassed = consoleOutput.includes('All loading state and user experience tests passed!');
      
      if (testsPassed) {
        console.log('\n🎉 All browser tests passed!');
      } else {
        console.log('\n⚠️ Some browser tests may have failed. Check the output above.');
      }
      
    } catch (error) {
      console.log('❌ Browser test execution failed:', error.message);
    }
  }

  async runAllTests() {
    await this.initialize();
    
    if (this.browser) {
      await this.runBrowserTests();
    }
    
    await this.cleanup();
  }

  generateSimplifiedSummary() {
    console.log('\n📋 Loading State Test Summary');
    console.log('=' .repeat(40));
    console.log('✅ Test files created and ready');
    console.log('✅ Contact component analyzed for loading state features');
    console.log('✅ Manual testing instructions provided');
    console.log('\n🎯 Next Steps:');
    console.log('1. Run manual tests using the HTML test runner');
    console.log('2. Verify all loading state functionality works as expected');
    console.log('3. Install Puppeteer for automated browser testing: npm install puppeteer');
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 Browser closed');
    }
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const runner = new LoadingStateTestRunner();
  runner.runAllTests().catch(console.error);
}

export default LoadingStateTestRunner;