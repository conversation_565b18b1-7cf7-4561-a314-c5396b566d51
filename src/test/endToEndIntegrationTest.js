/**
 * End-to-End Integration Test for EmailJS Contact Form
 * 
 * This test performs comprehensive end-to-end testing of the contact form
 * including form submission, email delivery, and user experience validation.
 * 
 * Requirements tested:
 * - 1.1: Complete user journey from form fill to email receipt
 * - 2.1: Email <NAME_EMAIL>
 * - 2.2: Professional email formatting
 * - 2.4: Structured email content
 * - 3.5: Consistent behavior across multiple submissions
 */

class EndToEndIntegrationTest {
    constructor() {
        this.testResults = [];
        this.emailDeliveryResults = [];
        this.consoleErrors = [];
        
        // Capture console errors during testing
        this.originalConsoleError = console.error;
        console.error = (...args) => {
            this.consoleErrors.push(args.join(' '));
            this.originalConsoleError(...args);
        };
    }

    /**
     * Test Case 1: Complete user journey with all fields filled
     */
    async testCompleteUserJourney() {
        console.log('🧪 Testing complete user journey with all fields...');
        
        const testData = {
            firstName: 'Sarah',
            lastName: '<PERSON>',
            email: '<EMAIL>',
            phone: '******-0123',
            eventType: 'wedding',
            eventDate: '2024-06-15',
            budget: '50k-100k',
            message: 'Planning a summer wedding for 150 guests. Looking for elegant floral arrangements and full event coordination.'
        };

        try {
            const result = await this.simulateFormSubmission(testData);
            
            this.testResults.push({
                test: 'Complete User Journey',
                status: result.success ? 'PASS' : 'FAIL',
                details: result.message,
                formData: testData,
                timestamp: new Date().toISOString()
            });

            if (result.success) {
                console.log('✅ Complete user journey test passed');
                return true;
            } else {
                console.log('❌ Complete user journey test failed:', result.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Complete user journey test error:', error);
            this.testResults.push({
                test: 'Complete User Journey',
                status: 'ERROR',
                details: error.message,
                formData: testData,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    /**
     * Test Case 2: Minimal valid submission (email only)
     */
    async testMinimalValidSubmission() {
        console.log('🧪 Testing minimal valid submission (email only)...');
        
        const testData = {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '',
            eventType: '',
            eventDate: '',
            budget: '',
            message: ''
        };

        try {
            const result = await this.simulateFormSubmission(testData);
            
            this.testResults.push({
                test: 'Minimal Valid Submission (Email Only)',
                status: result.success ? 'PASS' : 'FAIL',
                details: result.message,
                formData: testData,
                timestamp: new Date().toISOString()
            });

            if (result.success) {
                console.log('✅ Minimal valid submission test passed');
                return true;
            } else {
                console.log('❌ Minimal valid submission test failed:', result.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Minimal valid submission test error:', error);
            this.testResults.push({
                test: 'Minimal Valid Submission (Email Only)',
                status: 'ERROR',
                details: error.message,
                formData: testData,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    /**
     * Test Case 3: Phone-only submission
     */
    async testPhoneOnlySubmission() {
        console.log('🧪 Testing phone-only submission...');
        
        const testData = {
            firstName: 'Maria',
            lastName: 'Garcia',
            email: '',
            phone: '******-9876',
            eventType: 'corporate',
            eventDate: '2024-08-20',
            budget: '25k-50k',
            message: 'Corporate anniversary celebration for 200 employees.'
        };

        try {
            const result = await this.simulateFormSubmission(testData);
            
            this.testResults.push({
                test: 'Phone-Only Submission',
                status: result.success ? 'PASS' : 'FAIL',
                details: result.message,
                formData: testData,
                timestamp: new Date().toISOString()
            });

            if (result.success) {
                console.log('✅ Phone-only submission test passed');
                return true;
            } else {
                console.log('❌ Phone-only submission test failed:', result.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Phone-only submission test error:', error);
            this.testResults.push({
                test: 'Phone-Only Submission',
                status: 'ERROR',
                details: error.message,
                formData: testData,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    /**
     * Test Case 4: Multiple consecutive submissions
     */
    async testMultipleSubmissions() {
        console.log('🧪 Testing multiple consecutive submissions...');
        
        const testCases = [
            {
                name: 'Birthday Party',
                data: {
                    firstName: 'Alex',
                    lastName: 'Chen',
                    email: '<EMAIL>',
                    phone: '******-4567',
                    eventType: 'birthday',
                    eventDate: '2024-09-10',
                    budget: '10k-25k',
                    message: 'Sweet 16 birthday party with modern theme.'
                }
            },
            {
                name: 'Anniversary',
                data: {
                    firstName: 'Robert',
                    lastName: 'Smith',
                    email: '<EMAIL>',
                    phone: '******-7890',
                    eventType: 'anniversary',
                    eventDate: '2024-10-05',
                    budget: '25k-50k',
                    message: '25th wedding anniversary celebration.'
                }
            },
            {
                name: 'Other Celebration',
                data: {
                    firstName: 'Lisa',
                    lastName: 'Wong',
                    email: '<EMAIL>',
                    phone: '******-2468',
                    eventType: 'other',
                    eventDate: '2024-11-15',
                    budget: '50k-100k',
                    message: 'Graduation celebration for medical school.'
                }
            }
        ];

        let allPassed = true;
        const results = [];

        for (const testCase of testCases) {
            try {
                console.log(`  Testing ${testCase.name}...`);
                const result = await this.simulateFormSubmission(testCase.data);
                
                results.push({
                    name: testCase.name,
                    success: result.success,
                    message: result.message,
                    data: testCase.data
                });

                if (!result.success) {
                    allPassed = false;
                    console.log(`  ❌ ${testCase.name} failed: ${result.message}`);
                } else {
                    console.log(`  ✅ ${testCase.name} passed`);
                }

                // Wait between submissions to avoid rate limiting
                await this.delay(1000);
            } catch (error) {
                console.error(`  ❌ ${testCase.name} error:`, error);
                results.push({
                    name: testCase.name,
                    success: false,
                    message: error.message,
                    data: testCase.data
                });
                allPassed = false;
            }
        }

        this.testResults.push({
            test: 'Multiple Consecutive Submissions',
            status: allPassed ? 'PASS' : 'FAIL',
            details: `Tested ${testCases.length} consecutive submissions`,
            results: results,
            timestamp: new Date().toISOString()
        });

        if (allPassed) {
            console.log('✅ Multiple submissions test passed');
        } else {
            console.log('❌ Multiple submissions test failed');
        }

        return allPassed;
    }

    /**
     * Simulate form submission using EmailJS
     */
    async simulateFormSubmission(formData) {
        return new Promise((resolve) => {
            // Import EmailJS configuration
            const EMAILJS_CONFIG = {
                SERVICE_ID: 'service_s3o73s7',
                TEMPLATE_ID: 'template_xutydd8',
                PUBLIC_KEY: 'LJ0ZcxiBPMbk6VbgD'
            };

            // Prepare template parameters exactly as the Contact component does
            const templateParams = {
                from_name: `${formData.firstName} ${formData.lastName}`.trim(),
                from_email: formData.email,
                phone: formData.phone,
                event_type: formData.eventType || 'Not specified',
                event_date: formData.eventDate || 'Not specified',
                budget: formData.budget || 'Not specified',
                message: formData.message || 'No additional message provided',
                to_email: '<EMAIL>'
            };

            console.log('📧 Sending email with parameters:', templateParams);

            // Use EmailJS to send the email
            emailjs.send(
                EMAILJS_CONFIG.SERVICE_ID,
                EMAILJS_CONFIG.TEMPLATE_ID,
                templateParams,
                EMAILJS_CONFIG.PUBLIC_KEY
            )
            .then((response) => {
                console.log('✅ Email sent successfully:', response);
                
                this.emailDeliveryResults.push({
                    success: true,
                    response: response,
                    templateParams: templateParams,
                    timestamp: new Date().toISOString()
                });

                resolve({
                    success: true,
                    message: 'Email sent successfully',
                    response: response,
                    templateParams: templateParams
                });
            })
            .catch((error) => {
                console.error('❌ Email sending failed:', error);
                
                this.emailDeliveryResults.push({
                    success: false,
                    error: error,
                    templateParams: templateParams,
                    timestamp: new Date().toISOString()
                });

                resolve({
                    success: false,
                    message: `Email sending failed: ${error.message || error}`,
                    error: error,
                    templateParams: templateParams
                });
            });
        });
    }

    /**
     * Verify email formatting and professional appearance
     */
    verifyEmailFormatting() {
        console.log('🧪 Verifying email formatting and professional appearance...');
        
        const formatChecks = [];
        
        // Check if all required template variables are being sent
        const requiredFields = ['from_name', 'from_email', 'phone', 'event_type', 'event_date', 'budget', 'message', 'to_email'];
        
        if (this.emailDeliveryResults.length > 0) {
            const sampleEmail = this.emailDeliveryResults[0];
            const templateParams = sampleEmail.templateParams;
            
            requiredFields.forEach(field => {
                const hasField = templateParams.hasOwnProperty(field);
                formatChecks.push({
                    check: `Template includes ${field}`,
                    status: hasField ? 'PASS' : 'FAIL',
                    value: hasField ? templateParams[field] : 'Missing'
                });
            });

            // Check proper handling of empty fields
            const emptyFieldHandling = templateParams.event_type === 'Not specified' || 
                                     templateParams.event_date === 'Not specified' || 
                                     templateParams.budget === 'Not specified';
            
            formatChecks.push({
                check: 'Empty fields handled with "Not specified"',
                status: emptyFieldHandling ? 'PASS' : 'FAIL',
                value: 'Proper empty field handling detected'
            });

            // Check recipient email
            formatChecks.push({
                check: 'Correct recipient email',
                status: templateParams.to_email === '<EMAIL>' ? 'PASS' : 'FAIL',
                value: templateParams.to_email
            });

            console.log('✅ Email formatting verification completed');
        } else {
            formatChecks.push({
                check: 'Email delivery results available',
                status: 'FAIL',
                value: 'No email delivery results to verify'
            });
            console.log('❌ No email delivery results available for formatting verification');
        }

        this.testResults.push({
            test: 'Email Formatting Verification',
            status: formatChecks.every(check => check.status === 'PASS') ? 'PASS' : 'FAIL',
            details: 'Verified email template parameters and formatting',
            checks: formatChecks,
            timestamp: new Date().toISOString()
        });

        return formatChecks.every(check => check.status === 'PASS');
    }

    /**
     * Check for console errors during operation
     */
    checkConsoleErrors() {
        console.log('🧪 Checking for console errors during operation...');
        
        const hasErrors = this.consoleErrors.length > 0;
        
        this.testResults.push({
            test: 'Console Error Check',
            status: hasErrors ? 'FAIL' : 'PASS',
            details: hasErrors ? `Found ${this.consoleErrors.length} console errors` : 'No console errors detected',
            errors: this.consoleErrors,
            timestamp: new Date().toISOString()
        });

        if (hasErrors) {
            console.log(`❌ Found ${this.consoleErrors.length} console errors:`, this.consoleErrors);
        } else {
            console.log('✅ No console errors detected during testing');
        }

        return !hasErrors;
    }

    /**
     * Generate comprehensive test report
     */
    generateTestReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(test => test.status === 'PASS').length;
        const failedTests = this.testResults.filter(test => test.status === 'FAIL').length;
        const errorTests = this.testResults.filter(test => test.status === 'ERROR').length;

        const report = {
            summary: {
                totalTests,
                passedTests,
                failedTests,
                errorTests,
                successRate: `${((passedTests / totalTests) * 100).toFixed(1)}%`,
                timestamp: new Date().toISOString()
            },
            emailDelivery: {
                totalEmails: this.emailDeliveryResults.length,
                successfulDeliveries: this.emailDeliveryResults.filter(email => email.success).length,
                failedDeliveries: this.emailDeliveryResults.filter(email => !email.success).length
            },
            consoleErrors: {
                errorCount: this.consoleErrors.length,
                errors: this.consoleErrors
            },
            detailedResults: this.testResults,
            emailResults: this.emailDeliveryResults
        };

        return report;
    }

    /**
     * Utility function to add delay between operations
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Restore original console.error
     */
    cleanup() {
        console.error = this.originalConsoleError;
    }

    /**
     * Run all end-to-end integration tests
     */
    async runAllTests() {
        console.log('🚀 Starting End-to-End Integration Testing...\n');

        try {
            // Test 1: Complete user journey
            await this.testCompleteUserJourney();
            await this.delay(1500);

            // Test 2: Minimal valid submission
            await this.testMinimalValidSubmission();
            await this.delay(1500);

            // Test 3: Phone-only submission
            await this.testPhoneOnlySubmission();
            await this.delay(1500);

            // Test 4: Multiple submissions
            await this.testMultipleSubmissions();
            await this.delay(1000);

            // Verification tests
            this.verifyEmailFormatting();
            this.checkConsoleErrors();

            // Generate final report
            const report = this.generateTestReport();
            
            console.log('\n📊 END-TO-END INTEGRATION TEST REPORT');
            console.log('=====================================');
            console.log(`Total Tests: ${report.summary.totalTests}`);
            console.log(`Passed: ${report.summary.passedTests}`);
            console.log(`Failed: ${report.summary.failedTests}`);
            console.log(`Errors: ${report.summary.errorTests}`);
            console.log(`Success Rate: ${report.summary.successRate}`);
            console.log(`Email Deliveries: ${report.emailDelivery.successfulDeliveries}/${report.emailDelivery.totalEmails} successful`);
            console.log(`Console Errors: ${report.consoleErrors.errorCount}`);

            if (report.summary.passedTests === report.summary.totalTests && 
                report.emailDelivery.successfulDeliveries === report.emailDelivery.totalEmails &&
                report.consoleErrors.errorCount === 0) {
                console.log('\n🎉 ALL TESTS PASSED! End-to-end integration is working correctly.');
            } else {
                console.log('\n⚠️  Some tests failed. Please review the detailed results.');
            }

            return report;

        } catch (error) {
            console.error('❌ Critical error during testing:', error);
            return {
                error: true,
                message: error.message,
                timestamp: new Date().toISOString()
            };
        } finally {
            this.cleanup();
        }
    }
}

// Export for use in test runner
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EndToEndIntegrationTest;
}

// Auto-run if loaded directly in browser
if (typeof window !== 'undefined') {
    window.EndToEndIntegrationTest = EndToEndIntegrationTest;
}