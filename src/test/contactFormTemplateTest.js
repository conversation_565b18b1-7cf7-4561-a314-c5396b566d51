/**
 * Contact Form Template Parameter Test
 * 
 * This test verifies that the template parameter preparation logic in Contact.tsx
 * matches the expected EmailJS template variable mapping.
 */

// Test scenarios that mirror the Contact component's form data structure
const contactFormTestScenarios = [
  {
    name: 'Complete Contact Form Submission',
    description: 'All form fields filled with valid data',
    formData: {
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-123-4567',
      eventType: 'wedding',
      eventDate: '2024-06-15',
      budget: '25k-50k',
      message: 'Looking for an elegant outdoor wedding venue with capacity for 150 guests.'
    },
    expectedParams: {
      from_name: '<PERSON>',
      from_email: '<EMAIL>',
      phone: '******-123-4567',
      event_type: 'wedding',
      event_date: '2024-06-15',
      budget: '25k-50k',
      message: 'Looking for an elegant outdoor wedding venue with capacity for 150 guests.',
      to_email: '<EMAIL>'
    }
  },
  {
    name: '<PERSON><PERSON> Required Fields Only',
    description: 'Only name and email provided, all optional fields empty',
    formData: {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phone: '',
      eventType: '',
      eventDate: '',
      budget: '',
      message: ''
    },
    expectedParams: {
      from_name: 'Jane Smith',
      from_email: '<EMAIL>',
      phone: '',
      event_type: 'Not specified',
      event_date: 'Not specified',
      budget: 'Not specified',
      message: 'No additional message provided',
      to_email: '<EMAIL>'
    }
  },
  {
    name: 'Phone Contact Only',
    description: 'Name and phone provided, no email address',
    formData: {
      firstName: 'Mike',
      lastName: 'Johnson',
      email: '',
      phone: '******-987-6543',
      eventType: 'corporate',
      eventDate: '2024-08-20',
      budget: '50k-100k',
      message: 'Corporate anniversary celebration for 200 employees.'
    },
    expectedParams: {
      from_name: 'Mike Johnson',
      from_email: '',
      phone: '******-987-6543',
      event_type: 'corporate',
      event_date: '2024-08-20',
      budget: '50k-100k',
      message: 'Corporate anniversary celebration for 200 employees.',
      to_email: '<EMAIL>'
    }
  },
  {
    name: 'Partial Optional Fields',
    description: 'Some optional fields filled, others left empty',
    formData: {
      firstName: 'Sarah',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '',
      eventType: 'birthday',
      eventDate: '',
      budget: '10k-25k',
      message: ''
    },
    expectedParams: {
      from_name: 'Sarah Wilson',
      from_email: '<EMAIL>',
      phone: '',
      event_type: 'birthday',
      event_date: 'Not specified',
      budget: '10k-25k',
      message: 'No additional message provided',
      to_email: '<EMAIL>'
    }
  },
  {
    name: 'Edge Case: Single Name',
    description: 'Only first name provided, last name empty',
    formData: {
      firstName: 'Madonna',
      lastName: '',
      email: '<EMAIL>',
      phone: '******-111-2222',
      eventType: 'concert',
      eventDate: '2024-12-31',
      budget: '100k+',
      message: 'Exclusive private concert for VIP guests.'
    },
    expectedParams: {
      from_name: 'Madonna',
      from_email: '<EMAIL>',
      phone: '******-111-2222',
      event_type: 'concert',
      event_date: '2024-12-31',
      budget: '100k+',
      message: 'Exclusive private concert for VIP guests.',
      to_email: '<EMAIL>'
    }
  },
  {
    name: 'Edge Case: Empty Names with Spaces',
    description: 'Names with only spaces should be trimmed',
    formData: {
      firstName: '  John  ',
      lastName: '  Doe  ',
      email: '<EMAIL>',
      phone: '',
      eventType: '',
      eventDate: '',
      budget: '',
      message: ''
    },
    expectedParams: {
      from_name: 'John     Doe',
      from_email: '<EMAIL>',
      phone: '',
      event_type: 'Not specified',
      event_date: 'Not specified',
      budget: 'Not specified',
      message: 'No additional message provided',
      to_email: '<EMAIL>'
    }
  }
];

/**
 * Replicates the template parameter preparation logic from Contact.tsx
 * This should match exactly what the Contact component does
 */
function prepareContactFormTemplateParams(formData) {
  return {
    from_name: `${formData.firstName} ${formData.lastName}`.trim(),
    from_email: formData.email,
    phone: formData.phone,
    event_type: formData.eventType || 'Not specified',
    event_date: formData.eventDate || 'Not specified',
    budget: formData.budget || 'Not specified',
    message: formData.message || 'No additional message provided',
    to_email: '<EMAIL>'
  };
}

/**
 * Compares actual template parameters with expected parameters
 */
function compareTemplateParams(actual, expected, scenarioName) {
  const results = [];
  const allKeys = new Set([...Object.keys(actual), ...Object.keys(expected)]);
  
  for (const key of allKeys) {
    if (actual[key] === expected[key]) {
      results.push({
        type: 'success',
        field: key,
        message: `${key}: "${actual[key]}" ✅ matches expected`
      });
    } else {
      results.push({
        type: 'error',
        field: key,
        message: `${key}: Expected "${expected[key]}", got "${actual[key]}" ❌`
      });
    }
  }
  
  return results;
}

/**
 * Validates that all required template variables are present and correctly formatted
 */
function validateTemplateStructure(params) {
  const requiredFields = ['from_name', 'from_email', 'phone', 'event_type', 'event_date', 'budget', 'message', 'to_email'];
  const validationResults = [];
  
  // Check all required fields are present
  for (const field of requiredFields) {
    if (params.hasOwnProperty(field)) {
      validationResults.push({
        type: 'success',
        message: `✅ ${field} is present in template parameters`
      });
    } else {
      validationResults.push({
        type: 'error',
        message: `❌ ${field} is missing from template parameters`
      });
    }
  }
  
  // Validate specific field requirements
  if (params.to_email !== '<EMAIL>') {
    validationResults.push({
      type: 'error',
      message: `❌ to_email should be '<EMAIL>', got '${params.to_email}'`
    });
  }
  
  if (!params.from_name || params.from_name.trim() === '') {
    validationResults.push({
      type: 'error',
      message: '❌ from_name cannot be empty'
    });
  }
  
  // Check "Not specified" handling for optional fields
  const optionalFields = ['event_type', 'event_date', 'budget'];
  for (const field of optionalFields) {
    if (params[field] === '') {
      validationResults.push({
        type: 'warning',
        message: `⚠️ ${field} is empty (should be "Not specified" for better email formatting)`
      });
    }
  }
  
  if (params.message === '') {
    validationResults.push({
      type: 'warning',
      message: '⚠️ message is empty (should have default text for better email formatting)'
    });
  }
  
  return validationResults;
}

/**
 * Runs a single test scenario
 */
function runContactFormTemplateTest(scenario) {
  console.log(`\n🧪 Testing: ${scenario.name}`);
  console.log(`📝 Description: ${scenario.description}`);
  
  // Prepare template parameters using Contact component logic
  const actualParams = prepareContactFormTemplateParams(scenario.formData);
  
  console.log('\n📧 Generated Template Parameters:');
  console.log('─'.repeat(50));
  Object.entries(actualParams).forEach(([key, value]) => {
    console.log(`${key}: ${value || '(empty)'}`);
  });
  console.log('─'.repeat(50));
  
  // Compare with expected parameters
  const comparisonResults = compareTemplateParams(actualParams, scenario.expectedParams, scenario.name);
  console.log('\n🔍 Parameter Comparison:');
  comparisonResults.forEach(result => {
    const icon = result.type === 'success' ? '✅' : '❌';
    console.log(`  ${icon} ${result.message}`);
  });
  
  // Validate template structure
  const structureResults = validateTemplateStructure(actualParams);
  console.log('\n🏗️ Template Structure Validation:');
  structureResults.forEach(result => {
    const icon = result.type === 'success' ? '✅' : 
                result.type === 'error' ? '❌' : '⚠️';
    console.log(`  ${icon} ${result.message}`);
  });
  
  // Determine if test passed
  const hasErrors = comparisonResults.some(r => r.type === 'error') || 
                   structureResults.some(r => r.type === 'error');
  
  return {
    scenario: scenario.name,
    passed: !hasErrors,
    actualParams,
    expectedParams: scenario.expectedParams,
    comparisonResults,
    structureResults
  };
}

/**
 * Runs all contact form template tests
 */
function runAllContactFormTemplateTests() {
  console.log('🚀 Starting Contact Form Template Parameter Tests');
  console.log('=' .repeat(70));
  console.log('This test verifies that Contact.tsx template parameter preparation');
  console.log('matches the expected EmailJS template variable mapping.\n');
  
  const testResults = [];
  
  for (const scenario of contactFormTestScenarios) {
    const result = runContactFormTemplateTest(scenario);
    testResults.push(result);
  }
  
  // Summary
  console.log('\n📊 Test Summary');
  console.log('=' .repeat(70));
  
  const passedTests = testResults.filter(r => r.passed);
  const failedTests = testResults.filter(r => !r.passed);
  
  testResults.forEach((result, index) => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${index + 1}. ${result.scenario}: ${status}`);
  });
  
  console.log(`\n🎯 Overall Result: ${passedTests.length}/${testResults.length} tests passed`);
  
  if (failedTests.length === 0) {
    console.log('\n🎉 All contact form template tests passed!');
    console.log('\n✅ Verified Functionality:');
    console.log('  • Template parameter mapping matches Contact.tsx logic');
    console.log('  • "Not specified" handling for empty optional fields');
    console.log('  • Default message text for empty message field');
    console.log('  • Proper name concatenation and trimming');
    console.log('  • Correct recipient email address');
    console.log('  • All required template variables present');
  } else {
    console.log(`\n❌ ${failedTests.length} test(s) failed. Please review the results above.`);
  }
  
  return {
    totalTests: testResults.length,
    passedTests: passedTests.length,
    failedTests: failedTests.length,
    results: testResults
  };
}

// Export functions for use in other modules
export {
  runAllContactFormTemplateTests,
  runContactFormTemplateTest,
  prepareContactFormTemplateParams,
  compareTemplateParams,
  validateTemplateStructure,
  contactFormTestScenarios
};

// Command line execution
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  try {
    const results = runAllContactFormTemplateTests();
    if (results.failedTests === 0) {
      process.exit(0);
    } else {
      process.exit(1);
    }
  } catch (error) {
    console.error('\n❌ Test execution failed:', error);
    process.exit(1);
  }
}