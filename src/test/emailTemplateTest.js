/**
 * Email Template Testing Utility
 * 
 * This utility tests the EmailJS template functionality with various form data scenarios
 * to ensure proper variable mapping and "Not specified" handling for optional fields.
 */

import emailjs from '@emailjs/browser';
import { EMAILJS_CONFIG } from '../config/emailjs';

// Test data scenarios
const testScenarios = [
  {
    name: 'Complete Form Data',
    description: 'All fields filled with valid data',
    formData: {
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-123-4567',
      eventType: 'wedding',
      eventDate: '2024-06-15',
      budget: '25k-50k',
      message: 'Looking for an elegant outdoor wedding venue with capacity for 150 guests.'
    }
  },
  {
    name: 'Minimal Required Data',
    description: 'Only required fields (name and email) filled',
    formData: {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '',
      eventType: '',
      eventDate: '',
      budget: '',
      message: ''
    }
  },
  {
    name: 'Phone Only Contact',
    description: 'Name and phone provided, no email',
    formData: {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      email: '',
      phone: '******-987-6543',
      eventType: 'corporate',
      eventDate: '2024-08-20',
      budget: '50k-100k',
      message: 'Corporate anniversary celebration for 200 employees.'
    }
  },
  {
    name: 'Partial Optional Fields',
    description: 'Some optional fields filled, others empty',
    formData: {
      firstName: 'Sarah',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '',
      eventType: 'birthday',
      eventDate: '',
      budget: '10k-25k',
      message: ''
    }
  }
];

/**
 * Converts form data to EmailJS template parameters
 * This mirrors the logic in Contact.tsx
 */
function prepareTemplateParams(formData) {
  return {
    from_name: `${formData.firstName} ${formData.lastName}`.trim(),
    from_email: formData.email,
    phone: formData.phone,
    event_type: formData.eventType || 'Not specified',
    event_date: formData.eventDate || 'Not specified',
    budget: formData.budget || 'Not specified',
    message: formData.message || 'No additional message provided',
    to_email: '<EMAIL>'
  };
}

/**
 * Validates template parameters against expected format
 */
function validateTemplateParams(params, scenario) {
  const validationResults = [];
  
  // Check required fields
  if (!params.from_name || params.from_name.trim() === '') {
    validationResults.push('❌ from_name is empty or missing');
  } else {
    validationResults.push('✅ from_name is properly set');
  }
  
  if (!params.to_email || params.to_email !== '<EMAIL>') {
    validationResults.push('❌ to_email is not <NAME_EMAIL>');
  } else {
    validationResults.push('✅ to_email is correctly set');
  }
  
  // Check "Not specified" handling for optional fields
  const optionalFields = ['event_type', 'event_date', 'budget'];
  optionalFields.forEach(field => {
    if (params[field] === 'Not specified') {
      validationResults.push(`✅ ${field} correctly shows "Not specified" when empty`);
    } else if (params[field] && params[field] !== '') {
      validationResults.push(`✅ ${field} has value: "${params[field]}"`);
    } else {
      validationResults.push(`❌ ${field} is empty but should show "Not specified"`);
    }
  });
  
  // Check message handling
  if (params.message === 'No additional message provided') {
    validationResults.push('✅ message correctly shows default text when empty');
  } else if (params.message && params.message !== '') {
    validationResults.push(`✅ message has custom content: "${params.message}"`);
  } else {
    validationResults.push('❌ message is empty but should have default text');
  }
  
  // Check contact information
  if (params.from_email && params.from_email !== '') {
    validationResults.push(`✅ from_email is set: "${params.from_email}"`);
  } else {
    validationResults.push('ℹ️ from_email is empty (acceptable if phone is provided)');
  }
  
  if (params.phone && params.phone !== '') {
    validationResults.push(`✅ phone is set: "${params.phone}"`);
  } else {
    validationResults.push('ℹ️ phone is empty (acceptable if email is provided)');
  }
  
  return validationResults;
}

/**
 * Displays template parameters in a readable format
 */
function displayTemplateParams(params) {
  console.log('\n📧 Email Template Parameters:');
  console.log('─'.repeat(50));
  console.log(`To: ${params.to_email}`);
  console.log(`From Name: ${params.from_name}`);
  console.log(`From Email: ${params.from_email || '(not provided)'}`);
  console.log(`Phone: ${params.phone || '(not provided)'}`);
  console.log(`Event Type: ${params.event_type}`);
  console.log(`Event Date: ${params.event_date}`);
  console.log(`Budget: ${params.budget}`);
  console.log(`Message: ${params.message}`);
  console.log('─'.repeat(50));
}

/**
 * Tests a single scenario
 */
function testScenario(scenario) {
  console.log(`\n🧪 Testing: ${scenario.name}`);
  console.log(`📝 Description: ${scenario.description}`);
  
  const templateParams = prepareTemplateParams(scenario.formData);
  displayTemplateParams(templateParams);
  
  const validationResults = validateTemplateParams(templateParams, scenario);
  console.log('\n🔍 Validation Results:');
  validationResults.forEach(result => console.log(`  ${result}`));
  
  return templateParams;
}

/**
 * Checks if EmailJS is properly configured
 */
function checkEmailJSConfiguration() {
  console.log('🔧 Checking EmailJS Configuration...');
  
  const isConfigured = 
    EMAILJS_CONFIG.SERVICE_ID !== 'YOUR_SERVICE_ID' &&
    EMAILJS_CONFIG.TEMPLATE_ID !== 'YOUR_TEMPLATE_ID' &&
    EMAILJS_CONFIG.PUBLIC_KEY !== 'YOUR_PUBLIC_KEY';
  
  if (isConfigured) {
    console.log('✅ EmailJS is properly configured');
    console.log(`   Service ID: ${EMAILJS_CONFIG.SERVICE_ID}`);
    console.log(`   Template ID: ${EMAILJS_CONFIG.TEMPLATE_ID}`);
    console.log(`   Public Key: ${EMAILJS_CONFIG.PUBLIC_KEY.substring(0, 8)}...`);
    return true;
  } else {
    console.log('❌ EmailJS is not configured (using placeholder values)');
    return false;
  }
}

/**
 * Sends a test email (only if EmailJS is configured and user confirms)
 */
async function sendTestEmail(templateParams, scenarioName) {
  try {
    console.log(`\n📤 Sending test email for scenario: ${scenarioName}`);
    
    const result = await emailjs.send(
      EMAILJS_CONFIG.SERVICE_ID,
      EMAILJS_CONFIG.TEMPLATE_ID,
      templateParams,
      EMAILJS_CONFIG.PUBLIC_KEY
    );
    
    console.log('✅ Test email sent successfully!');
    console.log(`   Status: ${result.status}`);
    console.log(`   Text: ${result.text}`);
    return true;
  } catch (error) {
    console.log('❌ Failed to send test email:');
    console.log(`   Error: ${error.message}`);
    console.log(`   Status: ${error.status || 'Unknown'}`);
    return false;
  }
}

/**
 * Main test function
 */
export async function runEmailTemplateTests(sendActualEmails = false) {
  console.log('🚀 Starting Email Template Tests');
  console.log('=' .repeat(60));
  
  // Check configuration
  const isConfigured = checkEmailJSConfiguration();
  
  if (!isConfigured) {
    console.log('\n⚠️ EmailJS is not configured. Tests will run in simulation mode only.');
    sendActualEmails = false;
  }
  
  // Run all test scenarios
  const testResults = [];
  
  for (const scenario of testScenarios) {
    const templateParams = testScenario(scenario);
    testResults.push({
      scenario: scenario.name,
      params: templateParams,
      success: true
    });
    
    // Send actual email if requested and configured
    if (sendActualEmails && isConfigured) {
      const emailSent = await sendTestEmail(templateParams, scenario.name);
      testResults[testResults.length - 1].emailSent = emailSent;
      
      // Add delay between emails to avoid rate limiting
      if (emailSent) {
        console.log('⏳ Waiting 2 seconds before next test...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }
  
  // Summary
  console.log('\n📊 Test Summary');
  console.log('=' .repeat(60));
  testResults.forEach((result, index) => {
    console.log(`${index + 1}. ${result.scenario}: ✅ Template mapping validated`);
    if (result.emailSent !== undefined) {
      console.log(`   Email delivery: ${result.emailSent ? '✅ Success' : '❌ Failed'}`);
    }
  });
  
  console.log('\n🎯 Key Validation Points Checked:');
  console.log('  ✅ Template parameter mapping');
  console.log('  ✅ "Not specified" handling for empty optional fields');
  console.log('  ✅ Default message for empty message field');
  console.log('  ✅ Proper name concatenation');
  console.log('  ✅ Contact information handling');
  
  return testResults;
}

// Export individual functions for manual testing
export {
  testScenarios,
  prepareTemplateParams,
  validateTemplateParams,
  displayTemplateParams,
  checkEmailJSConfiguration,
  sendTestEmail
};