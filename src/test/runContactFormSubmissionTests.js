#!/usr/bin/env node

/**
 * Contact Form Submission Test Runner
 * 
 * This script runs comprehensive tests for the contact form submission functionality.
 * It can be executed from the command line to verify all form submission scenarios.
 * 
 * Usage:
 *   node src/test/runContactFormSubmissionTests.js
 * 
 * Requirements tested: 1.1, 1.2, 1.5, 2.1
 */

import { ContactFormTester, TEST_CONFIG } from './contactFormSubmissionTest.js';

async function main() {
    console.log('🚀 Contact Form Submission Test Suite');
    console.log('=====================================\n');
    
    console.log('📋 Task: Implement comprehensive form submission testing');
    console.log('🎯 Requirements: 1.1, 1.2, 1.5, 2.1\n');
    
    console.log('Test Scenarios:');
    console.log('1. ✅ Valid form submission with all fields populated');
    console.log('2. ✅ Minimal valid submission (email OR phone only)');
    console.log('3. ✅ Email delivery <NAME_EMAIL>');
    console.log('4. ✅ Form clearing and success message display after successful submission\n');
    
    try {
        const tester = new ContactFormTester();
        const results = await tester.runAllTests();
        
        // Exit with appropriate code
        process.exit(results.allPassed ? 0 : 1);
        
    } catch (error) {
        console.error('❌ Error running tests:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

// Handle command line execution
if (process.argv[1] === new URL(import.meta.url).pathname) {
    main();
}