/**
 * Form Validation Demonstration
 * Shows the validation logic in action with various test cases
 */

import FormValidationTester from './formValidationTest.js';

console.log('🎯 Form Validation Demonstration');
console.log('='.repeat(50));

const tester = new FormValidationTester();

// Demo 1: Invalid email format
console.log('\n📧 Demo 1: Invalid Email Format');
const invalidEmailForm = {
    firstName: 'John',
    lastName: 'Doe',
    email: 'invalid-email-format',
    phone: '',
    eventType: 'wedding',
    eventDate: '2024-12-25',
    budget: '25k-50k',
    message: 'Test message'
};

const errors1 = tester.validateForm(invalidEmailForm);
console.log('Form data:', JSON.stringify(invalidEmailForm, null, 2));
console.log('Validation errors:', JSON.stringify(errors1, null, 2));

// Demo 2: Invalid phone format
console.log('\n📱 Demo 2: Invalid Phone Format');
const invalidPhoneForm = {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '',
    phone: 'abc123',
    eventType: 'corporate',
    eventDate: '2024-11-15',
    budget: '50k-100k',
    message: 'Corporate event planning'
};

const errors2 = tester.validateForm(invalidPhoneForm);
console.log('Form data:', JSON.stringify(invalidPhoneForm, null, 2));
console.log('Validation errors:', JSON.stringify(errors2, null, 2));

// Demo 3: Missing contact information
console.log('\n❌ Demo 3: Missing Contact Information');
const noContactForm = {
    firstName: 'Bob',
    lastName: 'Johnson',
    email: '',
    phone: '',
    eventType: 'birthday',
    eventDate: '2024-10-30',
    budget: '10k-25k',
    message: 'Birthday party planning'
};

const errors3 = tester.validateForm(noContactForm);
console.log('Form data:', JSON.stringify(noContactForm, null, 2));
console.log('Validation errors:', JSON.stringify(errors3, null, 2));

// Demo 4: Valid form
console.log('\n✅ Demo 4: Valid Form');
const validForm = {
    firstName: 'Alice',
    lastName: 'Williams',
    email: '<EMAIL>',
    phone: '+1234567890',
    eventType: 'anniversary',
    eventDate: '2024-12-31',
    budget: '100k+',
    message: 'Anniversary celebration'
};

const errors4 = tester.validateForm(validForm);
console.log('Form data:', JSON.stringify(validForm, null, 2));
console.log('Validation errors:', JSON.stringify(errors4, null, 2));

// Demo 5: Edge case - only email provided
console.log('\n📧 Demo 5: Only Email Provided (Valid)');
const emailOnlyForm = {
    firstName: 'Charlie',
    lastName: 'Brown',
    email: '<EMAIL>',
    phone: '',
    eventType: 'other',
    eventDate: '',
    budget: '',
    message: 'Just need some information'
};

const errors5 = tester.validateForm(emailOnlyForm);
console.log('Form data:', JSON.stringify(emailOnlyForm, null, 2));
console.log('Validation errors:', JSON.stringify(errors5, null, 2));

// Demo 6: Edge case - only phone provided
console.log('\n📱 Demo 6: Only Phone Provided (Valid)');
const phoneOnlyForm = {
    firstName: 'Diana',
    lastName: 'Prince',
    email: '',
    phone: '+44 20 7946 0958',
    eventType: 'wedding',
    eventDate: '2025-06-15',
    budget: '50k-100k',
    message: 'International wedding planning'
};

const errors6 = tester.validateForm(phoneOnlyForm);
console.log('Form data:', JSON.stringify(phoneOnlyForm, null, 2));
console.log('Validation errors:', JSON.stringify(errors6, null, 2));

console.log('\n🎯 Validation Demonstration Complete');
console.log('All scenarios show how the form validation logic handles different input cases.');