#!/usr/bin/env node

/**
 * Command-line Email Template Test Runner
 * 
 * Usage:
 *   node runEmailTemplateTests.js                    # Run template mapping tests only
 *   node runEmailTemplateTests.js --send-emails     # Run tests and send actual emails
 */

// Test data scenarios
const testScenarios = [
  {
    name: 'Complete Form Data',
    description: 'All fields filled with valid data',
    formData: {
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-123-4567',
      eventType: 'wedding',
      eventDate: '2024-06-15',
      budget: '25k-50k',
      message: 'Looking for an elegant outdoor wedding venue with capacity for 150 guests.'
    }
  },
  {
    name: 'Minimal Required Data',
    description: 'Only required fields (name and email) filled',
    formData: {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '',
      eventType: '',
      eventDate: '',
      budget: '',
      message: ''
    }
  },
  {
    name: 'Phone Only Contact',
    description: 'Name and phone provided, no email',
    formData: {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      email: '',
      phone: '******-987-6543',
      eventType: 'corporate',
      eventDate: '2024-08-20',
      budget: '50k-100k',
      message: 'Corporate anniversary celebration for 200 employees.'
    }
  },
  {
    name: 'Partial Optional Fields',
    description: 'Some optional fields filled, others empty',
    formData: {
      firstName: 'Sarah',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '',
      eventType: 'birthday',
      eventDate: '',
      budget: '10k-25k',
      message: ''
    }
  }
];

// EmailJS Configuration (simulated - would normally import from config)
const EMAILJS_CONFIG = {
  SERVICE_ID: 'service_s3o73s7',
  TEMPLATE_ID: 'template_xutydd8',
  PUBLIC_KEY: 'LJ0ZcxiBPMbk6VbgD'
};

/**
 * Converts form data to EmailJS template parameters
 * This mirrors the logic in Contact.tsx
 */
function prepareTemplateParams(formData) {
  return {
    from_name: `${formData.firstName} ${formData.lastName}`.trim(),
    from_email: formData.email,
    phone: formData.phone,
    event_type: formData.eventType || 'Not specified',
    event_date: formData.eventDate || 'Not specified',
    budget: formData.budget || 'Not specified',
    message: formData.message || 'No additional message provided',
    to_email: '<EMAIL>'
  };
}

/**
 * Validates template parameters against expected format
 */
function validateTemplateParams(params, scenario) {
  const validationResults = [];
  
  // Check required fields
  if (!params.from_name || params.from_name.trim() === '') {
    validationResults.push({ type: 'error', message: 'from_name is empty or missing' });
  } else {
    validationResults.push({ type: 'success', message: 'from_name is properly set' });
  }
  
  if (!params.to_email || params.to_email !== '<EMAIL>') {
    validationResults.push({ type: 'error', message: 'to_email is not <NAME_EMAIL>' });
  } else {
    validationResults.push({ type: 'success', message: 'to_email is correctly set' });
  }
  
  // Check "Not specified" handling for optional fields
  const optionalFields = ['event_type', 'event_date', 'budget'];
  optionalFields.forEach(field => {
    if (params[field] === 'Not specified') {
      validationResults.push({ type: 'success', message: `${field} correctly shows "Not specified" when empty` });
    } else if (params[field] && params[field] !== '') {
      validationResults.push({ type: 'success', message: `${field} has value: "${params[field]}"` });
    } else {
      validationResults.push({ type: 'error', message: `${field} is empty but should show "Not specified"` });
    }
  });
  
  // Check message handling
  if (params.message === 'No additional message provided') {
    validationResults.push({ type: 'success', message: 'message correctly shows default text when empty' });
  } else if (params.message && params.message !== '') {
    validationResults.push({ type: 'success', message: `message has custom content: "${params.message}"` });
  } else {
    validationResults.push({ type: 'error', message: 'message is empty but should have default text' });
  }
  
  // Check contact information
  if (params.from_email && params.from_email !== '') {
    validationResults.push({ type: 'success', message: `from_email is set: "${params.from_email}"` });
  } else {
    validationResults.push({ type: 'info', message: 'from_email is empty (acceptable if phone is provided)' });
  }
  
  if (params.phone && params.phone !== '') {
    validationResults.push({ type: 'success', message: `phone is set: "${params.phone}"` });
  } else {
    validationResults.push({ type: 'info', message: 'phone is empty (acceptable if email is provided)' });
  }
  
  return validationResults;
}

/**
 * Displays template parameters in a readable format
 */
function displayTemplateParams(params) {
  console.log('\n📧 Email Template Parameters:');
  console.log('─'.repeat(50));
  console.log(`To: ${params.to_email}`);
  console.log(`From Name: ${params.from_name}`);
  console.log(`From Email: ${params.from_email || '(not provided)'}`);
  console.log(`Phone: ${params.phone || '(not provided)'}`);
  console.log(`Event Type: ${params.event_type}`);
  console.log(`Event Date: ${params.event_date}`);
  console.log(`Budget: ${params.budget}`);
  console.log(`Message: ${params.message}`);
  console.log('─'.repeat(50));
}

/**
 * Tests a single scenario
 */
function testScenario(scenario) {
  console.log(`\n🧪 Testing: ${scenario.name}`);
  console.log(`📝 Description: ${scenario.description}`);
  
  const templateParams = prepareTemplateParams(scenario.formData);
  displayTemplateParams(templateParams);
  
  const validationResults = validateTemplateParams(templateParams, scenario);
  console.log('\n🔍 Validation Results:');
  validationResults.forEach(result => {
    const icon = result.type === 'success' ? '✅' : 
                result.type === 'error' ? '❌' : 
                result.type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`  ${icon} ${result.message}`);
  });
  
  return {
    scenario: scenario.name,
    params: templateParams,
    validationResults: validationResults,
    success: !validationResults.some(r => r.type === 'error')
  };
}

/**
 * Checks if EmailJS is properly configured
 */
function checkEmailJSConfiguration() {
  console.log('🔧 Checking EmailJS Configuration...');
  
  const isConfigured = 
    EMAILJS_CONFIG.SERVICE_ID !== 'YOUR_SERVICE_ID' &&
    EMAILJS_CONFIG.TEMPLATE_ID !== 'YOUR_TEMPLATE_ID' &&
    EMAILJS_CONFIG.PUBLIC_KEY !== 'YOUR_PUBLIC_KEY';
  
  if (isConfigured) {
    console.log('✅ EmailJS is properly configured');
    console.log(`   Service ID: ${EMAILJS_CONFIG.SERVICE_ID}`);
    console.log(`   Template ID: ${EMAILJS_CONFIG.TEMPLATE_ID}`);
    console.log(`   Public Key: ${EMAILJS_CONFIG.PUBLIC_KEY.substring(0, 8)}...`);
    return true;
  } else {
    console.log('❌ EmailJS is not configured (using placeholder values)');
    return false;
  }
}

/**
 * Simulates sending a test email (for demonstration purposes)
 */
async function simulateEmailSend(templateParams, scenarioName) {
  console.log(`\n📤 [SIMULATION] Sending test email for scenario: ${scenarioName}`);
  console.log('   This would send an email with the following parameters:');
  
  // Display what would be sent
  Object.entries(templateParams).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  console.log('✅ [SIMULATION] Email would be sent successfully');
  return true;
}

/**
 * Main test function
 */
async function runEmailTemplateTests(sendActualEmails = false) {
  console.log('🚀 Starting Email Template Tests');
  console.log('=' .repeat(60));
  
  // Check configuration
  const isConfigured = checkEmailJSConfiguration();
  
  if (!isConfigured && sendActualEmails) {
    console.log('\n⚠️ EmailJS is not configured. Cannot send actual emails.');
    console.log('   Running in simulation mode instead.');
    sendActualEmails = false;
  }
  
  // Run all test scenarios
  const testResults = [];
  
  for (const scenario of testScenarios) {
    const result = testScenario(scenario);
    testResults.push(result);
    
    // Send email if requested
    if (sendActualEmails && isConfigured) {
      // Note: Actual email sending would require EmailJS library in Node.js environment
      console.log('\n⚠️ Actual email sending requires browser environment or EmailJS Node.js setup');
      console.log('   Use the HTML test runner for actual email testing');
    } else {
      await simulateEmailSend(result.params, scenario.name);
    }
  }
  
  // Summary
  console.log('\n📊 Test Summary');
  console.log('=' .repeat(60));
  
  const successCount = testResults.filter(r => r.success).length;
  const totalCount = testResults.length;
  
  testResults.forEach((result, index) => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${index + 1}. ${result.scenario}: ${status}`);
  });
  
  console.log(`\n🎯 Overall Result: ${successCount}/${totalCount} scenarios passed`);
  
  if (successCount === totalCount) {
    console.log('\n🎉 All template mapping tests passed successfully!');
    console.log('\n🎯 Key Validation Points Verified:');
    console.log('  ✅ Template parameter mapping');
    console.log('  ✅ "Not specified" handling for empty optional fields');
    console.log('  ✅ Default message for empty message field');
    console.log('  ✅ Proper name concatenation');
    console.log('  ✅ Contact information handling');
  } else {
    console.log('\n❌ Some tests failed. Please review the validation results above.');
  }
  
  return testResults;
}

// Command line execution
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  const sendEmails = process.argv.includes('--send-emails');
  
  console.log('📧 EmailJS Template Test Runner');
  console.log('─'.repeat(40));
  
  if (sendEmails) {
    console.log('Mode: Template testing + Email sending');
  } else {
    console.log('Mode: Template testing only');
    console.log('Use --send-emails flag to attempt actual email sending');
  }
  
  runEmailTemplateTests(sendEmails)
    .then(() => {
      console.log('\n✅ Testing completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Testing failed:', error);
      process.exit(1);
    });
}

export {
  runEmailTemplateTests,
  testScenarios,
  prepareTemplateParams,
  validateTemplateParams,
  checkEmailJSConfiguration
};