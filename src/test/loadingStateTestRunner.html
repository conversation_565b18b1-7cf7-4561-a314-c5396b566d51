<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading State and User Experience Tests</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #6b7280;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .test-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .test-info h3 {
            color: #0c4a6e;
            margin: 0 0 10px 0;
        }
        
        .test-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .test-info li {
            margin: 5px 0;
            color: #374151;
        }
        
        .requirements {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
        }
        
        .requirements h4 {
            color: #92400e;
            margin: 0 0 10px 0;
        }
        
        .btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: #6366f1;
        }
        
        .btn-secondary:hover {
            background: #4f46e5;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .status.running {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        
        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #22c55e;
        }
        
        .status.error {
            background: #fee2e2;
            color: #dc2626;
            border: 1px solid #ef4444;
        }
        
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .warning h4 {
            color: #92400e;
            margin: 0 0 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Loading State and User Experience Tests</h1>
        <p class="subtitle">Comprehensive testing of form submission loading states and user experience flow</p>
        
        <div class="test-info">
            <h3>📋 Test Coverage</h3>
            <p>This test suite verifies the following loading state functionality:</p>
            <ul>
                <li><strong>Loading Indicator:</strong> Verify loading indicator appears during form submission</li>
                <li><strong>Button Disable:</strong> Test submit button disable functionality to prevent duplicate submissions</li>
                <li><strong>State Cleanup (Success):</strong> Confirm proper loading state cleanup after successful submission</li>
                <li><strong>State Cleanup (Error):</strong> Confirm proper loading state cleanup after error</li>
                <li><strong>Complete UX Flow:</strong> Test user experience flow from submission to completion</li>
            </ul>
        </div>
        
        <div class="requirements">
            <h4>📋 Requirements Coverage</h4>
            <p><strong>Requirement 1.2:</strong> System displays success message and clears form fields</p>
            <p><strong>Requirement 3.4:</strong> System handles loading states appropriately with visual feedback</p>
            <p><strong>Requirement 4.4:</strong> System shows loading indicator and disables submit button during submission</p>
        </div>
        
        <div class="warning">
            <h4>⚠️ Test Environment Notice</h4>
            <p>These tests create a temporary form overlay to test loading states in isolation. The tests use mocked EmailJS responses to simulate different scenarios (success/error) with controlled timing.</p>
        </div>
        
        <button id="runTests" class="btn">🚀 Run Loading State Tests</button>
        <button id="clearConsole" class="btn btn-secondary">🧹 Clear Console</button>
        
        <div id="status"></div>
        <div id="console" class="console-output" style="display: none;"></div>
    </div>

    <script src="loadingStateTest.js"></script>
    <script>
        const runTestsBtn = document.getElementById('runTests');
        const clearConsoleBtn = document.getElementById('clearConsole');
        const statusDiv = document.getElementById('status');
        const consoleDiv = document.getElementById('console');
        
        let originalConsoleLog = console.log;
        
        // Override console.log to capture output
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.join(' ');
            consoleDiv.textContent += message + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        runTestsBtn.addEventListener('click', async () => {
            runTestsBtn.disabled = true;
            statusDiv.innerHTML = '<div class="status running">🔄 Running loading state tests...</div>';
            consoleDiv.style.display = 'block';
            consoleDiv.textContent = '';
            
            try {
                await window.loadingStateTests.initialize();
                await window.loadingStateTests.runAllTests();
                
                statusDiv.innerHTML = '<div class="status success">✅ Loading state tests completed! Check the console output above for detailed results.</div>';
            } catch (error) {
                console.log('❌ Test execution failed:', error.message);
                statusDiv.innerHTML = '<div class="status error">❌ Test execution failed. Check console for details.</div>';
            } finally {
                runTestsBtn.disabled = false;
            }
        });
        
        clearConsoleBtn.addEventListener('click', () => {
            consoleDiv.textContent = '';
            consoleDiv.style.display = 'none';
            statusDiv.innerHTML = '';
        });
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (window.loadingStateTests) {
                window.loadingStateTests.cleanup();
            }
        });
    </script>
</body>
</html>