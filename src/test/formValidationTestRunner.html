<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Validation Test Runner</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .run-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }
        .run-button:hover {
            background: #45a049;
        }
        .run-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        #console-output {
            background: #1e1e1e;
            color: #ffffff;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 600px;
            overflow-y: auto;
            border: 2px solid #333;
        }
        .summary-box {
            background: #e8f5e8;
            border: 2px solid #4CAF50;
            border-radius: 5px;
            padding: 20px;
            margin-top: 20px;
        }
        .summary-box.failed {
            background: #ffeaea;
            border-color: #f44336;
        }
        .test-info {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin-bottom: 20px;
        }
        .requirements-list {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin-top: 20px;
        }
        .requirements-list h3 {
            margin-top: 0;
            color: #e65100;
        }
        .requirements-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .requirements-list li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Form Validation Test Suite</h1>
        <p>Task 5: Test form validation and error handling scenarios</p>
        <p>Testing invalid email/phone formats, missing contact info, and error message clarity</p>
    </div>

    <div class="test-container">
        <div class="test-info">
            <h3>📋 Test Coverage</h3>
            <p>This test suite validates the following scenarios from the Contact form component:</p>
            <ul>
                <li><strong>Invalid Email Format Validation:</strong> Tests various invalid email formats and ensures they are rejected</li>
                <li><strong>Invalid Phone Format Validation:</strong> Tests various invalid phone formats and ensures they are rejected</li>
                <li><strong>Missing Contact Information:</strong> Tests scenarios where neither email nor phone is provided</li>
                <li><strong>Error Message Clarity:</strong> Verifies that error messages are clear and actionable</li>
                <li><strong>Field Highlighting Logic:</strong> Tests that error states are properly flagged for UI highlighting</li>
                <li><strong>Edge Cases:</strong> Tests boundary conditions and special cases</li>
            </ul>
        </div>

        <button id="runTests" class="run-button">🚀 Run Form Validation Tests</button>
        
        <div id="console-output"></div>
        
        <div class="requirements-list">
            <h3>📋 Requirements Coverage</h3>
            <ul>
                <li><strong>Requirement 1.3:</strong> Form validation for invalid email and phone formats</li>
                <li><strong>Requirement 1.4:</strong> Clear error messages for validation failures</li>
                <li><strong>Requirement 4.3:</strong> Field highlighting and error correction guidance</li>
            </ul>
        </div>
    </div>

    <script src="formValidationTest.js"></script>
    <script>
        // Custom console to capture output
        const originalConsole = console.log;
        const outputElement = document.getElementById('console-output');
        
        function customConsole(...args) {
            const message = args.join(' ');
            outputElement.textContent += message + '\n';
            outputElement.scrollTop = outputElement.scrollHeight;
            originalConsole(...args);
        }
        
        console.log = customConsole;
        
        document.getElementById('runTests').addEventListener('click', function() {
            const button = this;
            button.disabled = true;
            button.textContent = '🔄 Running Tests...';
            
            // Clear previous output
            outputElement.textContent = '';
            
            // Add timestamp
            console.log(`Test execution started at: ${new Date().toLocaleString()}`);
            console.log('');
            
            try {
                // Create and run the test suite
                const tester = new FormValidationTester();
                const results = tester.runAllTests();
                
                // Create summary box
                setTimeout(() => {
                    const summaryBox = document.createElement('div');
                    summaryBox.className = `summary-box ${results.failed > 0 ? 'failed' : ''}`;
                    
                    summaryBox.innerHTML = `
                        <h3>📊 Test Results Summary</h3>
                        <p><strong>Total Tests:</strong> ${results.totalTests}</p>
                        <p><strong>Passed:</strong> ${results.passed} ✅</p>
                        <p><strong>Failed:</strong> ${results.failed} ❌</p>
                        <p><strong>Success Rate:</strong> ${results.successRate.toFixed(1)}%</p>
                        ${results.failed > 0 ? 
                            `<p><strong>Status:</strong> ❌ Some tests failed - check console output for details</p>` : 
                            `<p><strong>Status:</strong> ✅ All tests passed successfully!</p>`
                        }
                    `;
                    
                    // Remove existing summary if present
                    const existingSummary = document.querySelector('.summary-box');
                    if (existingSummary) {
                        existingSummary.remove();
                    }
                    
                    document.querySelector('.test-container').appendChild(summaryBox);
                    
                    button.disabled = false;
                    button.textContent = '🚀 Run Form Validation Tests';
                }, 100);
                
            } catch (error) {
                console.log(`❌ Test execution failed: ${error.message}`);
                button.disabled = false;
                button.textContent = '🚀 Run Form Validation Tests';
            }
        });
        
        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('runTests').click();
            }, 500);
        });
    </script>
</body>
</html>