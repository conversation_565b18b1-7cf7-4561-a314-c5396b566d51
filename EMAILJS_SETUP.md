# EmailJS Setup Guide for Aurum Vesaire Contact Form

## Step 1: Create EmailJS Account
1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

## Step 2: Create Email Service
1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose "Gmail" (<NAME_EMAIL>)
4. Follow the authentication process to connect your Gmail account
5. Note down the **Service ID** (e.g., `service_abc123`)

## Step 3: Create Email Template
1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Use this template content:

**Subject:** New Contact Form Submission - {{event_type}}

**Body:**
```
Hello Aurum Vesaire Team,

You have received a new contact form submission from your website:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}

Event Details:
- Event Type: {{event_type}}
- Event Date: {{event_date}}
- Budget Range: {{budget}}

Message:
{{message}}

Please respond to this inquiry promptly.

Best regards,
Aurum Vesaire Website
```

4. Save the template and note down the **Template ID** (e.g., `template_xyz789`)

## Step 4: Get Public Key
1. Go to "Account" → "General"
2. Find your **Public Key** (e.g., `user_abcdef123456`)

## Step 5: Update Configuration
1. Open `src/config/emailjs.js`
2. Replace the placeholder values with your actual credentials:

```javascript
export const EMAILJS_CONFIG = {
  SERVICE_ID: 'your_actual_service_id',
  TEMPLATE_ID: 'your_actual_template_id',  
  PUBLIC_KEY: 'your_actual_public_key'
};
```

## Step 6: Test the Form
1. Start your development server: `npm start`
2. Navigate to the contact form
3. Fill out and submit a test message
4. Check <EMAIL> for the email

## Important Notes
- EmailJS free plan allows 200 emails per month
- Emails may take a few minutes to arrive
- Check spam folder if emails don't appear in inbox
- Keep your credentials secure and don't commit them to public repositories

## Troubleshooting
- If emails aren't sending, check browser console for errors
- Verify all credentials are correct
- Ensure Gmail service is properly authenticated
- Check EmailJS dashboard for usage limits and error logs