/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // Antique Gold Palette
        bronze: {
          50: '#faf7f2',
          100: '#f5ede0',
          200: '#ebd8c0',
          300: '#dfc096',
          400: '#cd7f32', // Primary bronze gold
          500: '#b8722c',
          600: '#a36327',
          700: '#8b5423',
          800: '#734620',
          900: '#5f3a1c',
        },
        goldenrod: {
          50: '#fffef5',
          100: '#fffce6',
          200: '#fff7c7',
          300: '#fff09e',
          400: '#daa520', // Secondary goldenrod
          500: '#c4941d',
          600: '#a67c17',
          700: '#8a6514',
          800: '#725316',
          900: '#5e4417',
        },
        saddlebrown: {
          50: '#faf6f2',
          100: '#f4ebe0',
          200: '#e7d4c0',
          300: '#d6b896',
          400: '#c19a6b',
          500: '#b0824a',
          600: '#a0703f',
          700: '#8b4513', // Accent saddle brown
          800: '#713a11',
          900: '#5c3010',
        },
        oldlace: {
          50: '#fdf5e6', // Background old lace
          100: '#fcf2dd',
          200: '#f9e8c7',
          300: '#f5dba6',
          400: '#f0cb7f',
          500: '#eab85c',
          600: '#e2a43e',
          700: '#d18f2a',
          800: '#b07424',
          900: '#8f5f22',
        },
        darkbrown: {
          50: '#f7f4f0',
          100: '#ede6db',
          200: '#dbc9b8',
          300: '#c4a68f',
          400: '#a8826a',
          500: '#926750',
          600: '#7d5544',
          700: '#654321', // Text dark brown
          800: '#533620',
          900: '#442d1c',
        }
      },
      fontFamily: {
        'serif': ['Playfair Display', 'serif'],
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      backgroundImage: {
        'gradient-gold': 'linear-gradient(135deg, #d4af37 0%, #f7b32b 100%)',
        'gradient-navy': 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
        'gradient-elegant': 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #d4af37 100%)',
      }
    },
  },
  plugins: [],
};
