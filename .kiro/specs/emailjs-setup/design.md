# Design Document

## Overview

The EmailJS setup involves configuring a third-party email service to handle contact form submissions from the Aurum Vesaire website. The existing contact form component already has the integration code in place, but requires proper EmailJS service configuration including account setup, email template creation, and credential configuration.

## Architecture

### Service Integration Flow
```
User Form Submission → EmailJS Client Library → EmailJS Service → Gmail SMTP → <EMAIL>
```

### Component Structure
- **Contact.tsx**: Existing React component with form handling and EmailJS integration
- **emailjs.js**: Configuration file containing EmailJ<PERSON> credentials
- **EmailJS Service**: External service handling email delivery
- **Gmail Account**: Destination email account (<EMAIL>)

## Components and Interfaces

### EmailJS Configuration Interface
```javascript
export const EMAILJS_CONFIG = {
  SERVICE_ID: string,    // EmailJS service identifier
  TEMPLATE_ID: string,   // Email template identifier  
  PUBLIC_KEY: string     // EmailJS public key for authentication
};
```

### Email Template Variables
The email template will receive the following parameters from the contact form:
- `from_name`: Combined first and last name
- `from_email`: User's email address
- `phone`: User's phone number
- `event_type`: Selected event type or "Not specified"
- `event_date`: Selected date or "Not specified"
- `budget`: Selected budget range or "Not specified"
- `message`: User's custom message or default text
- `to_email`: Fixed recipient (<EMAIL>)

### Form Validation Logic
The existing validation ensures:
- At least one contact method (email OR phone) is provided and valid
- Email format validation using regex pattern
- Phone format validation using international format regex
- Real-time error clearing when users correct invalid fields

## Data Models

### Form Data Structure
```typescript
interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  eventType: string;
  eventDate: string;
  budget: string;
  message: string;
}
```

### Email Template Structure
```
Subject: New Contact Form Submission - {{event_type}}

Body:
- Sender information (name, email, phone)
- Event details (type, date, budget)
- Custom message
- Professional formatting with clear sections
```

## Error Handling

### Configuration Validation
- Check if EmailJS credentials are properly set (not placeholder values)
- Display appropriate fallback behavior when service is not configured
- Log configuration issues to console for debugging

### Runtime Error Handling
- Network connectivity issues during email sending
- EmailJS service availability problems
- Invalid credential errors
- Rate limiting from EmailJS free tier (200 emails/month)

### User Experience During Errors
- Maintain form data when errors occur
- Provide clear, actionable error messages
- Offer alternative contact methods when service fails
- Show loading states during submission attempts

## Testing Strategy

### Manual Testing Approach
1. **Configuration Testing**: Verify EmailJS account setup and credential configuration
2. **Form Validation Testing**: Test all validation scenarios (valid/invalid email, phone, required fields)
3. **Email Delivery Testing**: Submit test forms and verify email <NAME_EMAIL>
4. **Error Scenario Testing**: Test behavior with invalid credentials, network issues
5. **User Experience Testing**: Verify loading states, success messages, form clearing

### Test Cases
- Valid form submission with all fields filled
- Valid form submission with minimal required fields (email OR phone)
- Invalid email format handling
- Invalid phone format handling
- Missing contact information handling
- Network error simulation
- EmailJS service unavailable simulation
- Form state management during submission process

### Success Criteria
- Test emails successfully <NAME_EMAIL>
- All form validation scenarios work correctly
- Error messages are user-friendly and actionable
- Form provides appropriate feedback for all user actions
- No console errors during normal operation

## Implementation Notes

### EmailJS Service Setup Requirements
1. Create EmailJS account at https://www.emailjs.com/
2. Set up Gmail service <NAME_EMAIL>
3. Create email template with proper variable mapping
4. Obtain and configure service credentials
5. Test email delivery functionality

### Security Considerations
- EmailJS public key is safe to expose in client-side code
- No sensitive credentials stored in repository
- Rate limiting handled by EmailJS service
- Form validation prevents malicious input

### Performance Considerations
- EmailJS requests are asynchronous and don't block UI
- Form submission includes loading states for user feedback
- Error handling prevents hanging submission states
- Form reset after successful submission improves user experience